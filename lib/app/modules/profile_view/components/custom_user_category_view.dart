import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/profile_view/controllers/profile_view_controller.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_post_model.dart';
import '../../../../services/app_link_service.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/comment_bottomsheet_view.dart';
import '../../../../utillites/common_post_like_bottomsheet.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/image_slider_show.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../chat_bot_view/views/chat_bot_view.dart';
import '../../explore/components/common_post_widget.dart';
import '../../explore/components/custom_icon_picker.dart';
import '../../explore/components/common_widget_view.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';

Padding floatingAction(BuildContext context, ProfileViewController controller) {
  return Padding(
    padding: EdgeInsets.only(right: MySize.size8 ?? 8),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SizedBox(
            width: MySize.size60,
            height: MySize.size60,
            child: FloatingActionButton(
              heroTag: 'action1',
              onPressed: () {
                HapticFeedback.lightImpact();
                ImagePickerBottomSheet.show(
                  context: context,
                  child: CustomIconPicker(
                    iconData: controller.iconData,
                    title: "What do you",
                    subtitle1: "want to",
                    subtitle2: " create?",
                    isProfile: true,
                  ),
                );
              },
              backgroundColor: AppTheme.primary1,
              shape: const CircleBorder(),
              child: Padding(
                padding: EdgeInsets.all(MySize.size17 ?? 17),
                child: SvgPicture.asset(
                  AppImage.editFloating,
                  width: MySize.size26,
                  height: MySize.size26,
                  color: AppTheme.white,
                ),
              ),
            )),
        Space.height(15),
        SizedBox(
          width: MySize.size60,
          height: MySize.size60,
          child: FloatingActionButton(
            heroTag: 'action2',
            onPressed: () {
              HapticFeedback.lightImpact();
              Get.bottomSheet(
                const ChatBotView(),
                isScrollControlled: true,
                // Allows full-screen height if needed
                backgroundColor: Colors.white,
                // Ensures background consistency
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
              );
            },
            backgroundColor: Color(0xFFe8ab2e),
            shape: const CircleBorder(),
            child: SvgPicture.asset(
              AppImage.chatBot,
            ),
          ),
        ),
        Space.height(10),
      ],
    ),
  );
}

Widget postDataView(BuildContext context, ProfileViewController controller) {
  return CustomSliverListView(
    emptyWidget: Padding(
      padding: EdgeInsets.only(
        top: MySize.size70 ?? 100,
        left: paddingHoriZontal,
        right: paddingHoriZontal,
      ),
      child: Padding(
        padding: EdgeInsets.only(bottom: 50),
        child: const Empty(
          title: "No Post available!",
        ),
      ),
    ),
    maximumReachedWidget: const SizedBox(),
    itemBuilder: (context, Post post, index) {
      Post res = controller.postDataList[index];
      return res.isPrivate == true
          ? const SizedBox()
          : commonFeedView(res, index, context,
              isProfile: true,
              onTap: () {
                Get.toNamed(Routes.post_detail, arguments: {
                  "postID": res.id,
                  "index": index,
                  "source": "profile"
                })?.then(
                  (value) async {
                    controller.page.value = 1;
                    controller.hasMoreData.value = true;
                    controller.postDataList.clear();
                    await controller.callApiForUserGetPost(
                      context: Get.context!,
                    );
                  },
                );
              },
              onLikeLongPress: () {
                controller.postLikeList.clear();

                controller.callApiForGetPostLike(
                    context: context, postId: res.id);
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                  ),
                  builder: (context) => CommonPostLikeBottomSheet(
                    index: index,
                    isLoading: controller.isLoading,
                    postLikeList: controller.postLikeList,
                  ),
                );
              },
              onShare: () {
                HapticFeedback.lightImpact();
                controller.isShare.value = true;
                AppLinkService().shareMedia(
                    slug: res.slug ?? '',
                    mediaType: ShareMediaType.posts,
                    title: res.title);
              },
              onLike: () => controller.callApiForLikeProject(
                    context: context,
                    postId: res.id.toString(),
                    index: index,
                  ),
              onComment: () {
                controller.callApiForGetCommentProject(
                    context: context, postId: res.id.toString(), index: index);
                showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(16))),
                    builder: (context) => CommonBottomSheet(
                          userId: res.user?.id ?? 0,
                          index: index,
                          isLoading: controller.isLoading,
                          commentController: controller.commentController,
                          commentDataList: controller.commentDataList,
                          commentFocusNode: controller.commentFocusNode,
                          onCommentSend: () {
                            controller.callApiForCommentProject(
                                context: context,
                                postId: "${controller.postDataList[index].id}",
                                index: index);
                            controller.commentController.clear();
                          },
                        )).then(
                  (value) {
                    controller.callApiForGetCommentProject(
                        context: context,
                        postId: res.id.toString(),
                        index: index);
                  },
                );
              },
              onBookmark: () => controller.callApiForBookMarkProject(
                    context: context,
                    postId: res.id.toString(),
                    index: index,
                  ),
              isLiked: res.isLiked?.value ?? false,
              likesCount: res.likesCount?.value ?? 0,
              isBookmarked: res.isBookMarked?.value ?? false,
              commentsCount: res.commentsCount?.value ?? 0,
              isUser: true, isShare: controller.isShare,);
    },
    isLoading: controller.apiManager.isLoading,
    items: controller.postDataList,
    hasMoreData: controller.hasMoreData.value,
    onLoadMore: () {
      return controller.callApiForUserGetPost(
        context: context,
      );
    },
  );
}

Widget draftPostDataView(
    BuildContext context, ProfileViewController controller) {
  return CustomSliverListView(
    emptyWidget: Padding(
      padding: EdgeInsets.only(
        top: MySize.size50 ?? 100,
        left: paddingHoriZontal,
        right: paddingHoriZontal,
      ),
      child: Padding(
        padding: EdgeInsets.only(bottom: 50),
        child: const Empty(
          title: "No drafts available!",
        ),
      ),
    ),
    maximumReachedWidget: const SizedBox(),
    itemBuilder: (context, Post post, index) {
      Post res = controller.draftDataList[index];
      List<Media> sortedMedia = List.from(res.media);
      sortedMedia.sort((a, b) =>
          (b.isCoverImage == true ? 1 : 0) - (a.isCoverImage == true ? 1 : 0));
      return res.isPrivate == true
          ? const SizedBox()
          : Padding(
              padding: EdgeInsets.only(
                left: MySize.getScaledSizeWidth(24),
                right: MySize.getScaledSizeWidth(24),
                top: MySize.getScaledSizeHeight(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  draftHeadView(res, context, controller: controller),
                  InkWell(
                    onTap: () {
                      Get.toNamed(Routes.post,
                          arguments: {"PostId": res.id, "isDraft": true,"isProfile": true})?.then(
                        (value) async {
                          print("api calling ==>");
                          controller.page.value = 1;
                          controller.hasMoreData.value = true;
                          controller.draftDataList.clear();
                          await controller.callApiForUserGetPost(
                            context: Get.context!,
                            isPublished: true,
                          );
                        },
                      );
                    },
                    child: SizedBox(
                      width: double.infinity,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: res.media.isEmpty
                                ? 0
                                : MySize.getScaledSizeHeight(15),
                          ),
                          res.media.isEmpty
                              ? const SizedBox()
                              : ImageSlideshow(
                                  width: double.infinity,
                                  height: MySize.getScaledSizeHeight(254),
                                  initialPage: 0,
                                  indicatorColor: sortedMedia.length == 1
                                      ? Colors.transparent
                                      : AppTheme.white,
                                  indicatorBackgroundColor:
                                      sortedMedia.length == 1
                                          ? Colors.transparent
                                          : AppTheme.white.withOpacity(0.50),
                                  indicatorRadius:
                                      MySize.getScaledSizeWidth(3.5),
                                  isLoop: false,
                                  autoPlayInterval: 0,
                                  children: [
                                    for (int i = 0;
                                        i < sortedMedia.length;
                                        i++) ...[
                                      Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(25),
                                        ),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(25),
                                          child: NetworkImageComponent(
                                            imageUrl: sortedMedia[i].link,
                                            // simmerHeight: MySize.getScaledSizeHeight(76),
                                            width:
                                                MySize.getScaledSizeWidth(90),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                          res.title == ""
                              ? const SizedBox()
                              : SizedBox(
                                  height: MySize.getScaledSizeHeight(20),
                                ),
                          res.title == ""
                              ? const SizedBox()
                              : TypoGraphy(
                                  text: res.title,
                                  level: 4,
                                ),
                          res.description == "" || res.description == "<br> "
                              ? const SizedBox()
                              : SizedBox(
                                  height: MySize.getScaledSizeHeight(8),
                                ),
                          res.description != "" || res.description != "<br> "
                              ? HtmlWidget(
                                  truncateHtmlWithLineLimit(
                                      cleanHtmlColorStyles(
                                      res.description
                                          .toString()
                                          .replaceAll("<a ",
                                              "<a style='font-weight: 600; color: #6D11D2; text-decoration: none;' ")
                                          .replaceAll("#FF", "#")),
                                      maxChars: 70),
                                  textStyle: TextStyle(
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  onTapUrl: (url) async {
                                    if (url == 'readMore') {
                                      Get.toNamed(Routes.post_detail,
                                          arguments: {
                                            "postID": res.id,
                                            "index": index
                                          });
                                    } else if (!await launchUrl(
                                        Uri.parse(url))) {
                                      throw Exception('Could not launch $url');
                                    }
                                    return true;
                                  },
                                  customWidgetBuilder: (element) {
                                    if (element.attributes["src"] != null) {
                                      return SizedBox();
                                    }
                                    return null;
                                  },
                                )
                              : const SizedBox(),
                          res.description == "" || res.description == "<br> "
                              ? const SizedBox()
                              : SizedBox(
                                  height: MySize.getScaledSizeHeight(5),
                                ),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      top: MySize.getScaledSizeHeight(
                          res.description == "" || res.description == "<br> "
                              ? 0
                              : 18),
                      bottom: MySize.getScaledSizeHeight(18),
                    ),
                  ),
                  Divider(
                    thickness: 1,
                    color: AppTheme.grey[50],
                    height: 1,
                  ),
                ],
              ),
            );
    },
    isLoading: controller.apiManager.isLoading,
    items: controller.draftDataList,
    hasMoreData: controller.hasMoreData.value,
    onLoadMore: () {
      return controller.callApiForUserGetPost(
          context: context, isPublished: true);
    },
  );
}

Widget bookMarkPostDataView(
    BuildContext context, ProfileViewController controller) {
  return CustomSliverListView(
    emptyWidget: Padding(
      padding: EdgeInsets.only(
        top: MySize.size50 ?? 100,
        left: paddingHoriZontal,
        right: paddingHoriZontal,
      ),
      child: Padding(
        padding: EdgeInsets.only(bottom: 50),
        child: const Empty(
          title: "No BookMark available!",
        ),
      ),
    ),
    maximumReachedWidget: const SizedBox(),
    itemBuilder: (context, Post post, index) {
      Post res = controller.BookMarkList[index];
      List<Media> sortedMedia = List.from(res.media);
      sortedMedia.sort((a, b) =>
          (b.isCoverImage == true ? 1 : 0) - (a.isCoverImage == true ? 1 : 0));
      return res.isPrivate == true
          ? const SizedBox()
          : commonFeedView(res, index, context,
              onRepostTap: () async {
                Navigator.pop(context);
                showCommonReportBottomSheet(
                  context: context,
                  title: "Report",
                  subTitle: "Why are you reporting this post?",
                  description:
                      "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                  options: controller.repostData,
                  onOptionTap: (selectedOption) async {
                    controller.selectedReason.value = selectedOption;
                    await controller.callApiForReportPost(
                      context: context,
                      postId: res.id.toString(),
                    );
                  },
                );
              },
              onLikeLongPress: () {
                controller.postLikeList.clear();

                controller.callApiForGetPostLike(
                    context: context, postId: res.id);
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                  ),
                  builder: (context) => CommonPostLikeBottomSheet(
                    index: index,
                    isLoading: controller.isLoading,
                    postLikeList: controller.postLikeList,
                  ),
                );
              },
              onShare: () {
                HapticFeedback.lightImpact();
                controller.isShare.value = true;
                AppLinkService().shareMedia(
                    slug: res.slug ?? '',
                    mediaType: ShareMediaType.posts,
                    title: res.title);
              },
              onLike: () => controller.callApiForLikeProject(
                  context: context,
                  postId: res.id.toString(),
                  index: index,
                  isBookmark: true),
              onComment: () {
                controller.commentController.clear();
                controller.callApiForGetCommentProject(
                    context: context,
                    postId: res.id.toString(),
                    index: index,
                    isBookMark: true);
                showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(16))),
                    builder: (context) => CommonBottomSheet(
                          index: index,
                          isLoading: controller.isLoading,
                          commentController: controller.commentController,
                          commentDataList: controller.commentDataList,
                          commentFocusNode: controller.commentFocusNode,
                          userId: res.user?.id ?? 0,
                          onCommentSend: () {
                            controller.callApiForCommentProject(
                                context: context,
                                postId: "${controller.postDataList[index].id}",
                                index: index,
                                isBookMark: true);
                            controller.commentController.clear();
                          },
                          isBookMark: true,
                        )).then(
                  (value) {
                    controller.callApiForGetCommentProject(
                        context: context,
                        postId: res.id.toString(),
                        index: index,
                        isBookMark: true);
                  },
                );
              },
              onBookmark: () => controller.callApiForBookMarkProject(
                  context: context,
                  postId: res.id.toString(),
                  index: index,
                  isBookmark: true),
              isLiked: res.isLiked?.value ?? false,
              likesCount: res.likesCount?.value ?? 0,
              isBookmarked: res.isBookMarked?.value ?? false,
              commentsCount: res.commentsCount?.value ?? 0,
              isUser: true, isShare: controller.isShare,);
    },
    isLoading: controller.apiManager.isLoading,
    items: controller.BookMarkList,
    hasMoreData: controller.hasMoreData.value,
    onLoadMore: () {
      return controller.callApiForGetBookMarkProject(
        context: context,
      );
    },
  );
}
