import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/project_detail_view/controllers/project_detail_view_controller.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/network_image.dart';
import '../../../routes/app_pages.dart';
import '../../explore/components/common_widget_view.dart';
import '../../profile_view/components/profile_widget_view.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';

Widget buildProjectImage(
    {required String imageUrl, required String defaultImageUrl}) {
  return imageUrl.isNotEmpty
      ? NetworkImageComponent(
          imageUrl: imageUrl,
          height: MySize.getScaledSizeHeight(182),
          width: double.infinity,
          simmerHeight: MySize.getScaledSizeHeight(182),
        )
      : Image.asset(
          defaultImageUrl,
          height: MySize.getScaledSizeHeight(182),
          width: double.infinity,
          fit: BoxFit.cover,
        );
}

Widget buildTopGradient() {
  return Container(
    width: double.infinity,
    height: MySize.getScaledSizeHeight(182),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          AppTheme.black.withOpacity(0.8),
          AppTheme.black.withOpacity(0),
        ],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      ),
    ),
  );
}

Widget buildTopBar(BuildContext context,
    {required ProjectDetailViewController controller}) {
  return Column(
    children: [
      Space.height(55),
      Padding(
        padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
        child: Row(
          children: [
            InkWell(
              child: buildIcon(AppImage.backArrow, () {
                if(controller.args != null && controller.args['isBottom'] != null) {
                  Get.offAllNamed(Routes.Bottom_Bar, arguments: {"index": 1});
                } else {
                  Get.back();
                }
              }, padding: 7),
            ),
            Spacer(),
            buildMoreOptionsIcon(context, controller: controller),
          ],
        ),
      ),
    ],
  );
}

Widget buildMoreOptionsIcon(BuildContext context,
    {required ProjectDetailViewController controller}) {
  final data = controller.getProjectDetailData.value;
  final canEdit = CurrentUser.user.id == data.userId;

  return buildIcon(AppImage.moreVertIcon, () {
    HapticFeedback.lightImpact();
    ImagePickerBottomSheet.show(
      context: context,
      child: canEdit
          ? showPostEditOption(
              projectName: data.name,
              userId: data.userId,
              projectId: data.id.toString(),
              projectSlug: data.slug,
              isProjectEdit: true,
              isNotDelete: data.projectMembers.isNotEmpty &&
                  data.projectMembers.first.access == "write",
              isPrivate: data.isPrivate,
              isHide: data.hide ?? false,
              hidePrivate: () {
                controller.callApiForHideProject(
                  context: context,
                  projectId: data.id.toString(),
                  isHide: !(data.hide ?? false),
                );
              },
              isShare: controller.isShare,
              onShareContentType: (contentType) {
                controller.shareContentType.value = contentType;
              },
            )
          : showPostOption(
              projectId: data.id,
              projectSlug: data.slug,
              isProject: true,
              onTap: () {
                Navigator.pop(context);
                showCommonReportBottomSheet(
                  context: context,
                  title: "Report",
                  subTitle: "Why are you reporting this project?",
                  description:
                      "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                  options: controller.repostData,
                  onOptionTap: (selectedOption) async {
                    controller.selectedReason.value = selectedOption;
                    await controller.callApiForReportProject(
                      context: context,
                      projectId: controller.projectId.value.toString(),
                    );
                  },
                );
              },
              title: data.name,
              postId: data.id,
              isShare: controller.isShare,
              onShareContentType: (contentType) {
                controller.shareContentType.value = contentType;
              },
            ),
    );
  }, padding: 9);
}

Widget buildPrivateLockIcon({double? height}) {
  return Image.asset(
    AppImage.lockProject,
    height: MySize.getScaledSizeHeight(height ?? 30),
    width: MySize.getScaledSizeWidth(height ?? 30),
  );
}
