import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_post_model.dart';
import '../../../../models/section_base.dart';
import '../../../../services/app_link_service.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/comment_bottomsheet_view.dart';
import '../../../../utillites/common_post_like_bottomsheet.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/common_shimmer_effect.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../routes/app_pages.dart';
import '../../explore/components/common_post_widget.dart';
import '../controllers/global_search_view_controller.dart';

class GlobalSearchPostSection extends SectionBase<GlobalSearchViewController> {
  GlobalSearchPostSection({required super.controller});

  @override
  String get title => 'Posts';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return SizedBox();
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      Obx(() {
        return controller.isExploreLoading.value &&
                controller.postDataList.isEmpty
            ? SliverToBoxAdapter(
                child: ListView.builder(
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                // Disable scrolling inside CustomScrollView
                shrinkWrap: true,
                itemCount: 5,
                itemBuilder: (context, index) => ShimmerPostCard(),
              ))
            : controller.searchController.value.text.isNotEmpty &&
                    controller.postDataList.isEmpty
                ? SliverToBoxAdapter(
                    child: Padding(
                      padding:
                          EdgeInsets.only(top: MySize.getScaledSizeHeight(250)),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            "assets/images/icon_search.svg",
                            height: MySize.size50,
                            color: AppTheme.whiteWithNull,
                          ),
                          Empty(
                            title: "Search Result Not Found !",
                          ),
                        ],
                      ),
                    ),
                  )
                : CustomSliverListView(
                    emptyWidget: Padding(
                      padding: EdgeInsets.only(
                        top: MySize.getScaledSizeHeight(250),
                      ),
                      child: const Empty(
                        title: "Follow People or Project show Feed!",
                      ),
                    ),
                    maximumReachedWidget: const SizedBox(),
                    itemBuilder: (context, Post post, index) {
                      Post res = controller.postDataList[index];
                      return res.isPrivate == true
                          ? const SizedBox()
                          : Obx(
                              () => commonFeedView(
                                res,
                                index,
                                context,
                                onShare: () {
                                  HapticFeedback.lightImpact();
                                  controller.isShare.value = true;
                                  AppLinkService().shareMedia(
                                      slug: res.slug ?? '',
                                      mediaType: ShareMediaType.posts,
                                      title: res.title);
                                },
                                onLikeLongPress: () {
                                  controller.postLikeList.clear();

                                  controller.callApiForGetPostLike(
                                      context: context, postId: res.id);
                                  showModalBottomSheet(
                                    context: context,
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(16),
                                      ),
                                    ),
                                    builder: (context) =>
                                        CommonPostLikeBottomSheet(
                                      index: index,
                                      isLoading: controller.isLoading,
                                      postLikeList: controller.postLikeList,
                                    ),
                                  );
                                },
                                onTap: () {
                                  Get.toNamed(Routes.post_detail, arguments: {
                                    "postID": res.id,
                                    "index": index,
                                    "source": "globalSearch"
                                  })?.then(
                                    (value) {
                                      controller.page.value = 1;
                                      controller.hasMoreData.value = true;
                                      controller.postDataList.clear();
                                      return controller.callApiForExplorePost(
                                        context: context,
                                      );
                                    },
                                  );
                                },
                                onRepostTap: () async {
                                  Navigator.pop(context);
                                  showCommonReportBottomSheet(
                                    context: context,
                                    title: "Report",
                                    subTitle:
                                        "Why are you reporting this post?",
                                    description:
                                        "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                                    options: controller.repostData,
                                    onOptionTap: (selectedOption) async {
                                      controller.selectedReason.value =
                                          selectedOption;
                                      await controller.callApiForReportPost(
                                        context: context,
                                        postId: res.id.toString(),
                                      );
                                    },
                                  );
                                },
                                onLike: () => controller.callApiForLikeProject(
                                  context: context,
                                  postId: res.id.toString(),
                                  index: index,
                                ),
                                onComment: () {
                                  controller.commentController.clear();
                                  controller.callApiForGetCommentProject(
                                      context: context,
                                      postId: res.id.toString(),
                                      index: index);
                                  showModalBottomSheet(
                                    context: context,
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(16),
                                      ),
                                    ),
                                    builder: (context) => CommonBottomSheet(
                                      index: index,
                                      isLoading: controller.isLoading,
                                      commentController:
                                          controller.commentController,
                                      commentDataList:
                                          controller.commentDataList,
                                      commentFocusNode:
                                          controller.commentFocusNode,
                                      userId: res.user?.id ?? 0,
                                      onCommentSend: () {
                                        controller.callApiForCommentProject(
                                            context: context,
                                            postId:
                                                "${controller.postDataList[index].id}",
                                            index: index);
                                        controller.commentController.clear();
                                      },
                                    ),
                                  ).then((value) {
                                    controller.callApiForGetCommentProject(
                                        context: context,
                                        postId: res.id.toString(),
                                        index: index);
                                  },);
                                },
                                onBookmark: () =>
                                    controller.callApiForBookMarkProject(
                                  context: context,
                                  postId: res.id.toString(),
                                  index: index,
                                ),
                                isLiked: res.isLiked?.value ?? false,
                                likesCount: res.likesCount?.value ?? 0,
                                isBookmarked: res.isBookMarked?.value ?? false,
                                commentsCount: res.commentsCount?.value ?? 0, isShare: controller.isShare,
                              ),
                            );
                    },
                    isLoading: controller.isExploreLoading.value,
                    items: controller.postDataList,
                    hasMoreData: controller.hasMoreData.value,
                    onLoadMore: () {
                      return controller.callApiForExplorePost(
                        context: context,
                      );
                    },
                  );
      })
    ];
  }

  @override
  void onCategorySelected() {
    controller.page.value = 1;
    controller.postDataList.clear();
    controller.hasMoreData.value = true;
    controller.callApiForExplorePost(context: Get.context!);
  }
}
