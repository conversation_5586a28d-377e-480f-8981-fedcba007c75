import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/section_base.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/common_shimmer_grid_view.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/loader.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../explore/components/common_widget_view.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../controllers/global_search_view_controller.dart';

class GlobalSearchProjectSection extends SectionBase<GlobalSearchViewController> {
  GlobalSearchProjectSection({required super.controller});

  @override
  String get title => 'Projects';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return SizedBox();
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      SliverToBoxAdapter(
        child: Space.height(20),
      ),
      Obx(
            () {
          return controller.isProjectLoading.value &&
              controller.createdProject.isEmpty
              ? SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: MySize.size30 ?? 30),
                child: ShimmerGridView(),
              ))
              : controller.searchController.value.text
              .isNotEmpty &&
              controller.createdProject.isEmpty
              ? SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.only(
                  top: MySize.getScaledSizeHeight(250)),
              child: Column(
                mainAxisAlignment:
                MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    "assets/images/icon_search.svg",
                    height: MySize.size50,
                    color: AppTheme.whiteWithNull,
                  ),
                  Empty(
                    title: "Search Result Not Found !",
                  ),
                ],
              ),
            ),
          )
              : SliverPadding(
            padding: EdgeInsets.symmetric(
                horizontal:
                MySize.getScaledSizeWidth(30)),
            sliver: SliverGrid(
              gridDelegate:
              SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: MySize.size20 ?? 20,
                mainAxisSpacing: MySize.size10 ?? 30,
                childAspectRatio: MediaQuery.of(context).size.width > 350 ? 0.75 : 0.70,
              ),
              delegate: SliverChildBuilderDelegate(
                    (context, index) {
                  var project =
                  controller.createdProject[index];
                  return Container(
                    // color: Colors.red,
                    child: InkWell(
                      onTap: () {
                        if (project.isPrivate) {
                          CommonFunction
                              .showCustomSnackbar(
                            message:
                            "This Project is Private.",
                          );
                        } else {
                          Get.toNamed(
                              Routes.project_detail,
                              arguments: {
                                "projectId": project.id
                              })?.then((value) {
                            controller.projectPage.value = 1;
                            controller.hasMoreData.value =
                            true;
                            controller.createdProject
                                .clear();
                            controller.fetchProjectData(
                                context: context);
                          });
                        }
                      },
                      child: Column(
                        crossAxisAlignment:
                        CrossAxisAlignment.start,
                        children: [
                          Stack(
                            children: [
                              Container(
                                height: MySize.size142,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  borderRadius:
                                  BorderRadius
                                      .circular(25),
                                ),
                                child:
                                project.image.isNotEmpty
                                    ? ClipRRect(
                                  borderRadius:
                                  BorderRadius
                                      .circular(
                                      25),
                                  child:
                                  NetworkImageComponent(
                                    imageUrl:
                                    project
                                        .image,
                                    simmerHeight:
                                    MySize
                                        .size142,
                                    width: double
                                        .infinity,
                                  ),
                                )
                                    : Image.asset(
                                  AppImage
                                      .defaultImage,
                                  width: double
                                      .infinity,
                                  height: MySize
                                      .size142,
                                ),
                              ),
                              // if (project.userId == CurrentUser.user.id || (project.projectMembers.isNotEmpty &&
                              //     project.projectMembers[0].access == "write"))
                              if (project.userId == CurrentUser.user.id || (project.projectMembers.isNotEmpty || !project.isPrivate))
                                Positioned(
                                right: MySize
                                    .getScaledSizeWidth(
                                    12.11),
                                top: MySize
                                    .getScaledSizeHeight(
                                    12),
                                child: InkWell(
                                  onTap: () {
                                    HapticFeedback
                                        .lightImpact();
                                    ImagePickerBottomSheet.show(
                                        context: context,
                                        child:
                                        showPostOption(
                                          isProject:
                                          true,
                                          onTap: () {
                                            Navigator.pop(context);
                                            showCommonReportBottomSheet(
                                              context: context,
                                              title: "Report",
                                              subTitle: "Why are you reporting this project?",
                                              description:
                                              "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                                              options: controller.repostData,
                                              onOptionTap: (selectedOption) async {
                                                controller.selectedReason.value = selectedOption;
                                                await controller.callApiForReportProject(
                                                  context: context,
                                                  projectId: project.id.toString(),
                                                );
                                              },
                                            );
                                          }, isShare: controller.isShare,
                                        ));
                                  },
                                  child: SvgPicture.asset(
                                    AppImage.moreVertIcon,
                                    color: AppTheme.white,
                                    height: MySize.size24,
                                    width: MySize.size24,
                                  ),
                                ),
                              )
                            ],
                          ),
                          Space.height(6),
                          // TypoGraphy(
                          //   text:
                          //       '${project.subProjectsCount} Sub-projects',
                          //   level: 2,
                          //   fontWeight: FontWeight.w400,
                          // ),
                          Space.height(4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (project.isPrivate) ...[
                                Image.asset(
                                  AppImage.lockProject,
                                  height: MySize
                                      .getScaledSizeHeight(
                                      32),
                                  width: MySize
                                      .getScaledSizeWidth(
                                      32),
                                ),
                                Space.width(8)
                              ],
                              Expanded(
                                child: TypoGraphy(
                                  text: project.name,
                                  textStyle: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w700,
                                    fontFamily: 'Inter',
                                  ),
                                  overflow:
                                  TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
                childCount:
                controller.createdProject.length,
              ),
            ),
          );
        },
      ),
      if (controller.hasMoreData.value)
        SliverToBoxAdapter(
          child: SizedBox(
            height: 60, // Ensures enough space for the loader
            child: Center(
              child: Loader(),
            ),
          ),
        ),
    ];
  }

  @override
  void onCategorySelected() {
    controller.projectPage.value = 1;
    controller.hasMoreData.value = true;
    controller.createdProject.clear();
    controller.fetchProjectData(context: Get.context!);
  }
}
