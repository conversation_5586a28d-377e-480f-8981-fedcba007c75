import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:flutter_quill_delta_from_html/parser/html_to_delta.dart';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;
import 'package:incenti_ai/app/modules/bottom_bar/controllers/bottom_bar_controller.dart';
import 'package:incenti_ai/app/modules/community_detail_view/controllers/community_detail_view_controller.dart';
import 'package:incenti_ai/app/modules/explore/controllers/explore_controller.dart';
import 'package:incenti_ai/app/modules/profile_view/controllers/profile_view_controller.dart';
import 'package:incenti_ai/app/modules/project_detail_view/controllers/project_detail_view_controller.dart';
import 'package:incenti_ai/models/app_project_model.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:path/path.dart' as path;
import 'package:dio/dio.dart';
import 'dart:io' as io show Directory, File, Platform;
import '../../../../constants/api.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../constants/constant.dart';
import '../../../../main.dart';
import '../../../../models/app_post_model.dart';
import '../../../../models/mention_models.dart';
import '../../../../services/api_manager.dart';
import '../../../../services/spell_checker_class.dart';
import '../../../../utillites/api_use_dialog.dart';
import '../../../../utillites/common_function.dart';
import '../../../routes/app_pages.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;

import '../../explore/components/common_post_widget.dart';
import '../../sub_project_detail_view/controllers/sub_project_detail_view_controller.dart';
import '../components/html_common_rel.dart';

class PostController extends GetxController {
  late FocusNode editorFocusNode = FocusNode();
  late ScrollController editorScrollController = ScrollController();

  // late ScrollController parentScrollController = ScrollController();
  TextEditingController titleController = TextEditingController();
  RxString htmlContent = "".obs;
  RxList imageList = [].obs;
  RxString coverPhoto = "".obs;
  RxInt coverImageIndex = 0.obs;
  RxInt visibilityValue = 0.obs;
  RxString visibilityName = "Public".obs;
  RxString titleValue = "".obs;
  RxString htmlValue = "".obs;
  ApiManager apiManager = ApiManager();
  FocusNode titleFocusNode = FocusNode();
  var args = Get.arguments;
  Rxn<Project> project = Rxn<Project>();
  Rx<SubProjectData?> subProject = Rx<SubProjectData?>(null);
  RxBool isLoading = false.obs;
  RxBool isDraftLoading = false.obs;
  RxBool isCrossButton = false.obs;
  RxBool isPhotoButtonVisible = true.obs;
  RxBool isGetPostLoading = false.obs;
  RxBool isButtonVisible = true.obs;
  Rx<Post> getPostDetailData = Post().obs;
  RxInt postId = (-1).obs;
  RxInt communityId = (-1).obs;
  int lastKnownLength = 0;
  RxBool isSpellCheckEnabled = true.obs;
  RxBool isChecking = false.obs;
  RxMap<String, List<String>> suggestions = <String, List<String>>{}.obs;
  LanguageToolService spellCheckService = LanguageToolService();
  Color? selectedColor;
  static const String spellErrorKey = 'spell-error';
  RxBool showMentionDropdown = false.obs;
  RxList<MentionModel> mentionDropdownList = <MentionModel>[].obs;
  RxList<MentionModel> mentionList = <MentionModel>[].obs;
  bool justInsertedMention = false;
  TextEditingController mentionSearchController = TextEditingController();
  Timer? _mentionDebounce;
  RxBool isMentionLoading = false.obs;
  DateTime _lastEditTime = DateTime.now();
  final _debounceInterval = const Duration(milliseconds: 500);
  Rx<TextEditingController> messageController = TextEditingController().obs;
  var messages = "".obs;
  // final String socketUrl = 'https://stag-backend.incenti.ai';
  final String socketUrl = 'https://backend.floment.ai';
  IO.Socket? socket;
  RxBool isStreaming = false.obs;
  RxString streamedResponse = "".obs;
  RxInt lastResponseTimestamp = 0.obs;
  final ScrollController scrollController = ScrollController();

  late QuillController descriptionController = () {
    return QuillController.basic(
      config: QuillControllerConfig(
        clipboardConfig: QuillClipboardConfig(
          enableExternalRichPaste: io.Platform.isAndroid,
          onDeltaPaste: (delta) async {
            final cleanedOps = delta.toJson().map((op) {
              final opMap = Map<String, dynamic>.from(op);
              final attrs =
                  Map<String, dynamic>.from(opMap['attributes'] ?? {});

              // Remove background color
              attrs.remove('background');

              // Optional: force dark text
              attrs['color'] = box.read('isDarkMode') ? '#FFFFFF' : '#000000';

              if (attrs.isEmpty) {
                opMap.remove('attributes');
              } else {
                opMap['attributes'] = attrs;
              }

              return opMap;
            }).toList();

            final cleanedDelta = Delta.fromJson(cleanedOps);


            return cleanedDelta; // Return modified delta to insert into editor
          },
          onImagePaste: (imageBytes) async {
            if (kIsWeb) {
              return null;
            }
            final newFileName =
                'image-file-${DateTime.now().toIso8601String()}.png';
            final newPath = path.join(
              io.Directory.systemTemp.path,
              newFileName,
            );
            final file = await io.File(
              newPath,
            ).writeAsBytes(
              imageBytes,
              flush: true,
            );
            return file.path;
          },
        ),
      ),
    );
  }();

  bool wasPasteAction(int currentLength) {
    return currentLength > lastKnownLength + 5;
  }

  @override
  void onInit() {
    _initSocketConnection();
    descriptionController.addListener(() {
      descriptionController.skipRequestKeyboard = true;
      _onTextChanged();
      // _detectAndFormatUrls();
      _onEditorChanged();
    });

    editorFocusNode.addListener(() {
      if (editorFocusNode.hasFocus) {
        _setBoldAtSelection();
      }
    });
    if (args != null && args["PostId"] != null) {
      postId.value = args["PostId"];
    }
    if (args != null && args["communityId"] != null) {
      communityId.value = args["communityId"];
    }
    if (args != null &&
        args["isCreateProject"] != null &&
        args != null &&
        args["projectModel"] != null) {
      project.value = args["projectModel"];
    }
    if (postId.value != -1) {
      callApiForGetOnePost(context: Get.context!);
    }
    ever(lastResponseTimestamp, (_) {
      if (isStreaming.value && lastResponseTimestamp.value > 0) {
        Future.delayed(Duration(milliseconds: 1500), () {
          int currentTime = DateTime.now().millisecondsSinceEpoch;
          if (currentTime - lastResponseTimestamp.value > 1000 &&
              isStreaming.value) {
            _finalizeStreamedMessage();
          }
        });
      }
    });
    super.onInit();
  }

  void _initSocketConnection() {
    try {
      _disconnectSocket();

      socket = IO.io(socketUrl, <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': true,
      });

      socket?.onConnect((_) {});

      socket?.onDisconnect((_) {
        isStreaming.value = false;
        if (streamedResponse.value.isNotEmpty) {
          _finalizeStreamedMessage();
        }
      });

      socket?.onError((error) {
        isStreaming.value = false;
        isLoading.value = false;
        if (streamedResponse.value.isNotEmpty) {
          _finalizeStreamedMessage();
        }
      });

      socket?.on('connect_error', (error) {
        isStreaming.value = false;
        isLoading.value = false;
      });

      socket?.on('chatChunk', (data) {
        _handleSocketMessage(data);
      });

      socket?.connect();
    } catch (e) {
      isLoading.value = false;
    }
  }

  void _finalizeStreamedMessage() {
    if (streamedResponse.value.isNotEmpty) {
      String finalResponse = streamedResponse.value;
      messages.value = finalResponse;

      streamedResponse.value = "";
      isStreaming.value = false;
      isLoading.value = false;
      _scrollToBottom();
      lastResponseTimestamp.value = 0;
    }
  }

  void _handleSocketMessage(dynamic data) {
    isLoading.value = false;
    lastResponseTimestamp.value = DateTime.now().millisecondsSinceEpoch;

    try {
      if (data is Map) {
        if (data.containsKey('content')) {
          streamedResponse.value += data['content'];
          isStreaming.value = true;
          _scrollToBottom();
        }
      } else if (data is String) {
        try {
          Map<String, dynamic> jsonData = json.decode(data);
          if (jsonData.containsKey('content')) {
            streamedResponse.value += jsonData['content'];
            isStreaming.value = true;
            _scrollToBottom();
          }
        } catch (e) {
          streamedResponse.value += data;
          isStreaming.value = true;
          _scrollToBottom();
        }
      }
    } catch (e) {}
  }

  void _scrollToBottom() {
    Future.delayed(Duration(milliseconds: 100), () {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _disconnectSocket() {
    socket?.disconnect();
    socket?.dispose();
    socket = null;
  }

  void sendMessage(BuildContext context) {
    isLoading.value = true;
    String userMessage = messageController.value.text.trim();
    if (userMessage.isEmpty) return;
    messages.value = userMessage;

    // messageController.clear();
    _scrollToBottom();

    streamedResponse.value = "";
    isStreaming.value = true;

    Map<String, String> conversationPayload = {
      "role": "user",
      "content": userMessage,
    };

    _sendMessageViaSocket(conversationPayload);
  }

  void _sendMessageViaSocket(Map<String, String> conversation) {
    try {
      if (socket == null || socket!.disconnected) {

        _initSocketConnection();
        Future.delayed(Duration(milliseconds: 300), () {
          _emitSocketMessage(conversation);
        });
      } else {

        _emitSocketMessage(conversation);
      }
    } catch (e) {
      isStreaming.value = false;

      messages.value =
          "Failed to connect. Please check your internet connection and try again.";
    }
  }

  Future<void> _emitSocketMessage(Map<String, String> conversation) async {
    Map<String, dynamic> payload = {
      "conversations": [conversation]
    };

    socket?.emit('sendMessage', payload);
  }

  void insertMentionInHtml(
      {required String name, required String slug, required String type}) {
    final selection = descriptionController.selection;
    if (!selection.isValid) return;
    final text = descriptionController.document.toPlainText();
    final beforeCursor = text.substring(0, selection.baseOffset);
    final atIndex = beforeCursor.lastIndexOf('@');
    if (atIndex == -1) return;

    String mentionText = '@$name';

    descriptionController.replaceText(
      atIndex,
      selection.baseOffset - atIndex,
      '$mentionText ',
      TextSelection.collapsed(offset: atIndex + mentionText.length + 1),
    );

    String href = type == 'user' ? '/user/$slug' : '/projects/$slug';
    descriptionController.formatText(
      atIndex,
      mentionText.length,
      LinkAttribute(href),
    );

    showMentionDropdown.value = false;
    mentionDropdownList.clear();
    _mentionDebounce?.cancel();
  }

  void _onEditorChanged() {
    final text = descriptionController.document.toPlainText();
    final selection = descriptionController.selection;

    if (!selection.isValid || selection.baseOffset <= 0) {
      showMentionDropdown.value = false;
      mentionDropdownList.clear();
      return;
    }

    final beforeCursor = text.substring(0, selection.baseOffset);
    final atIndex = beforeCursor.lastIndexOf('@');

    if (atIndex == -1 ||
        (atIndex > 0 &&
            beforeCursor[atIndex - 1] != ' ' &&
            beforeCursor[atIndex - 1] != '\n')) {
      showMentionDropdown.value = false;
      mentionDropdownList.clear();
      return;
    }

    final textAfterAt = beforeCursor.substring(atIndex);
    if (textAfterAt.contains(' ')) {
      showMentionDropdown.value = false;
      mentionDropdownList.clear();
      return;
    }

    final mentionQuery = beforeCursor.substring(atIndex + 1).trim();
    bool isInMentionContext = selection.baseOffset > atIndex;

    if (isInMentionContext) {
      log("Searching with query: $mentionQuery");
      searchMentions(mentionQuery);
      showMentionDropdown.value = true;
    } else {
      showMentionDropdown.value = false;
      mentionDropdownList.clear();
    }
  }

  void searchMentions(String query) {
    isMentionLoading.value = true;
    _mentionDebounce?.cancel();

    _mentionDebounce = Timer(const Duration(milliseconds: 500), () async {
      if (!showMentionDropdown.value) return;

      try {
        final result = await _searchUsersAndProjects(query);
        if (showMentionDropdown.value) {
          mentionDropdownList.value = result;
          showMentionDropdown.value = result.isNotEmpty;
        }
      } catch (e) {
        debugPrint("searchMentions error: $e");
      } finally {
        isMentionLoading.value = false;
      }
    });
  }

  Future<List<MentionModel>> _searchUsersAndProjects(String query) async {
    final List<MentionModel> mentionList = [];
    try {
      final completer = Completer<List<MentionModel>>();

      await apiManager.callApi(
        ApiModel("/general/users-and-projects", APIType.GET),
        params: {
          "searchQuery": query,
        },
        successCallback: (response, message) async {
          final users = response["data"]["users"] ?? [];
          final projects = response["data"]["projects"] ?? [];

          for (var user in users) {
            mentionList.add(MentionModel(
              name: "${user["firstName"] ?? ''} ${user["lastName"] ?? ''}",
              slug: user["slug"] ?? '',
              userId: user["id"] ?? 0,
              image: user["image"] ?? '',
              type: 'user',
            ));
          }

          for (var project in projects) {
            mentionList.add(MentionModel(
              name: project["name"] ?? '',
              slug: project["slug"] ?? '',
              userId: project["User"]?["id"] ?? 0,
              image: project["image"] ?? '',
              type: 'project',
              projectId: project["id"],
            ));
          }

          completer.complete(mentionList);
        },
        failureCallback: (message, statusCode) {
          completer.complete(mentionList);
        },
      );

      return completer.future;
    } catch (e) {
      debugPrint("searchUsersAndProjects error: $e");
      return mentionList;
    }
  }

/*
  Future<List<MentionModel>> _searchUsers(String query) async {
    final List<MentionModel> users = [];
    try {
      final completer = Completer<List<MentionModel>>();
      await apiManager.callApi(
        APIS.followers.getFollowings,
        params: {
          "UserId": CurrentUser.user.id,
          "searchQuery": query,
          "limit": 10,
          "page": 1,
        },
        successCallback: (response, message) async {
          log("response followings ===> $response");
          if (response['status'] == 'success') {
            FollowersResponse followersResponse =
                FollowersResponse.fromJson(response, isFollower: false);
            for (FollowersData user in followersResponse.data?.data ?? []) {
              users.add(MentionModel(
                name:
                    "${user.followingUser?.firstName ?? ''} ${user.followingUser?.lastName ?? ''}",
                slug: user.followingUser?.slug ?? '',
                userId: user.followingUser?.id ?? 0,
                image: user.followingUser?.image ?? '',
                type: 'user',
              ));
            }
          }
          completer.complete(users);
        },
        failureCallback: (message, statusCode) {
          completer.complete(users);
        },
      );
      return completer.future;
    } catch (e) {
      return users;
    }
  }

  Future<List<MentionModel>> _searchProjects(String query,
      {bool followedBy = false, bool createBy = false}) async {
    final List<MentionModel> projects = [];
    try {
      final completer = Completer<List<MentionModel>>();
      await apiManager.callApi(
        APIS.project.getAllMyProject,
        params: {
          "searchQuery": query,
          "followed": 1,
          "UserId": CurrentUser.user.id,
          "limit": 10,
          "page": 1,
        },
        successCallback: (response, message) async {
          ProjectResponse projectResponse = ProjectResponse.fromJson(response);
          List<Project> userProject = projectResponse.data.data
              .where((project) =>
                  (project.projectMembers.isEmpty ||
                      project.projectMembers
                          .any((member) => member.access != "read")) &&
                  !project.isPrivate)
              .toList();
          for (final project in userProject) {
            projects.add(MentionModel(
              name: project.name,
              slug: project.slug ?? '',
              userId: project.userId,
              image: project.image,
              type: 'project',
              projectId: project.id,
            ));
          }

          completer.complete(projects);
        },
        failureCallback: (message, statusCode) {
          completer.complete(projects);
        },
      );
      return completer.future;
    } catch (e) {
      return projects;
    }
  }
*/

  void _setBoldAtSelection() {
    final selection = descriptionController.selection;

    if (!selection.isCollapsed) {
      // Apply bold to selected range
      descriptionController.formatText(
        selection.start,
        selection.end - selection.start,
        Attribute.bold,
      );
    } else {
      // Set bold as default for future text input
      descriptionController.formatSelection(Attribute.bold);
    }
  }

  String normalizeHtml(String html) {
    return html.replaceAll("<br>", "").trim();
  }

  List<String> extractImageUrls(String content) {
    RegExp exp = RegExp(r'https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|bmp|webp|svg)',
        caseSensitive: false);
    Iterable<RegExpMatch> matches = exp.allMatches(content);
    return matches.map((match) => match.group(0)!).toList();
  }

  List<String> extractYouTubeUrls(String content) {
    final iframeRegex = RegExp(
      r'<iframe[^>]*src="https://(?:www\.)?(?:youtube\.com/embed/|youtu\.be/)([a-zA-Z0-9_-]+)(?:\?[^"]*)?[^>]*></iframe>',
      caseSensitive: false,
    );

    final matches = iframeRegex.allMatches(content);
    List<String> youtubeUrls = [];

    for (final match in matches) {
      final videoId = match.group(1);
      if (videoId != null) {
        youtubeUrls.add('https://www.youtube.com/watch?v=$videoId');
      }
    }

    return youtubeUrls;
  }

  String? extractYouTubeVideoId(String url) {
    final patterns = [
      RegExp(r'^https:\/\/(?:www\.|m\.)?youtube\.com\/watch\?v=([_\-a-zA-Z0-9]{11}).*$'),
      RegExp(r'^https:\/\/(?:music\.)?youtube\.com\/watch\?v=([_\-a-zA-Z0-9]{11}).*$'),
      RegExp(r'^https:\/\/(?:www\.|m\.)?youtube\.com\/shorts\/([_\-a-zA-Z0-9]{11}).*$'),
      RegExp(r'^https:\/\/(?:www\.|m\.)?youtube(?:-nocookie)?\.com\/embed\/([_\-a-zA-Z0-9]{11}).*$'),
      RegExp(r'^https:\/\/youtu\.be\/([_\-a-zA-Z0-9]{11}).*$')
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(url);
      if (match != null && match.groupCount >= 1) {
        return match.group(1);
      }
    }
    return null;
  }

  Future<double> getMaxImageHeight(List imageList, double maxWidth) async {
    List<double> heights = await Future.wait(
      imageList.map((item) async {
        if (item is String && (item.contains('youtube.com') || item.contains('youtube'))) {
          return 200.0;
        }
        if (item is Map && (item['path']?.toString().contains('youtube.com') ?? false ||
            item['path']!.toString().contains('youtube') ?? false)) {
          return 200.0;
        }

        String url = item['path'];
        Completer<double> completer = Completer();
        Image image = Image.network(url);

        image.image.resolve(const ImageConfiguration()).addListener(
          ImageStreamListener((ImageInfo info, bool _) {
            double aspectRatio = info.image.width / info.image.height;
            log("aspectRatio == $aspectRatio");
            log("info.image.width == ${info.image.width}");
            log("info.image.height == ${info.image.height}");
            double displayHeight = maxWidth / aspectRatio;
            log("displayHeight == $displayHeight");

            if (displayHeight > MySize.getScaledSizeHeight(520)) {
              displayHeight = MySize.getScaledSizeHeight(520);
            }
            completer.complete(displayHeight);
          }),
        );

        return completer.future;
      }),
    );

    // Return the max height from the list
    return heights.isEmpty
        ? MySize.getScaledSizeHeight(520)
        : heights.reduce((a, b) => a > b ? a : b);
  }

  Future<double> getImageDisplayHeight(String imageUrl, double maxWidth) async {
    if(imageUrl.contains('youtube.com') || imageUrl.contains('youtube')) {
      return 200.0;
    }
    final Completer<double> completer = Completer();
    final Image image = Image.network(imageUrl);

    image.image.resolve(const ImageConfiguration()).addListener(
          ImageStreamListener(
            (ImageInfo info, bool _) {
              double aspectRatio = info.image.width / info.image.height;
              double displayHeight = maxWidth / aspectRatio;
              double maxHeight = 520;
              if (displayHeight > maxHeight) {
                displayHeight = maxHeight;
              }

              completer.complete(displayHeight);
            },
            onError: (error, stackTrace) {
              completer.complete(0);
            },
          ),
        );

    return completer.future;
  }

  String cleanHeaderBrs(String html) {
    html = html.replaceAllMapped(
      RegExp(r'</h[1-6]>\s*<p>\s*(<br\s*/?>\s*){1}\s*</p>\s*<h[1-6]>',
          caseSensitive: false),
      (match) {
        return match.group(0)!.replaceAll(
            RegExp(r'<p>\s*(<br\s*/?>\s*){1}\s*</p>', caseSensitive: false),
            '');
      },
    );

    html = html.replaceFirst(
        RegExp(r'(<br\s*/?>\s*)+(</?p>\s*)*$', caseSensitive: false), '');

    html = html.replaceAllMapped(
      RegExp(r'<(h[1-6])>(.*?)<\/\1>', caseSensitive: false, dotAll: true),
      (match) {
        final tag = match.group(1)!;
        var content = match.group(2)!;
        content = content.replaceFirst(
            RegExp(r'^(\s*<br\s*/?>\s*)+', caseSensitive: false), '');
        content = content.replaceFirst(
            RegExp(r'(<br\s*/?>\s*)+$', caseSensitive: false), '');
        return '<$tag>$content</$tag>';
      },
    );

    html = html.replaceAllMapped(
      RegExp(r'([^>])(<h[1-6]>)', caseSensitive: false),
      (match) => '${match.group(1)}<br>${match.group(2)}',
    );

    return html;
  }

  Future<void> updateImageList(String content) async {
    List<String> extractedImages = extractImageUrls(content);
    List<String> extractedVideos = extractYouTubeUrls(content);

    List<String> allExtractedMedia = [...extractedImages, ...extractedVideos];

    imageList.removeWhere((media) =>
        media.containsKey("isExtracted") &&
        !allExtractedMedia.contains(media["path"]));

    List<Map<String, dynamic>> updatedList = [];

    for (var imageUrl in extractedImages) {
      bool alreadyExists = imageList.any((media) => media["path"] == imageUrl);
      if (!alreadyExists) {
        double height = await getImageDisplayHeight(imageUrl, MySize.screenWidth - 40);
        updatedList.add({
          "path": imageUrl,
          "isCover": imageList.isEmpty && updatedList.isEmpty,
          "type": "image",
          "isCustom": false,
          "isExtracted": true,
          'height': height
        });
      }
    }

    for (var videoUrl in extractedVideos) {
      bool alreadyExists = imageList.any((media) => media["path"] == videoUrl);
      if (!alreadyExists) {
        updatedList.add({
          "path": videoUrl,
          "isCover": imageList.isEmpty && updatedList.isEmpty,
          "type": "video",
          "isCustom": false,
          "isExtracted": true,
          'height': 200.0
        });
      }
    }

    if (updatedList.isNotEmpty) {
      imageList.addAll(updatedList);
      imageList.toSet().toList();
    }

    if (imageList.isEmpty) {
      coverPhoto.value = "";
      coverImageIndex.value = -1;
    } else {
      coverPhoto.value = imageList.first["path"];
      coverImageIndex.value = 0;
    }
  }

  callApiForPostView(
      {required BuildContext context, required String imageFiles}) async {
    if (imageFiles.isEmpty) return;
    app.resolve<CustomDialogs>().showCircularDialog(context);
    try {
      FormData formData = FormData.fromMap({
        "file": await MultipartFile.fromFile(imageFiles,
            filename: imageFiles.split('/').last.trim()),
      });

      apiManager.callApi(
        APIS.generalImageUpload.imageUpload,
        params: formData,
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            isPhotoButtonVisible.value = false;
            List<dynamic> uploadedImages = response['data'];
            for (var imageData in uploadedImages) {
              String imageUrl = imageData['link'];
              double height = await getImageDisplayHeight(
                  imageUrl, MySize.screenWidth - 40);
              imageList.insert(0, {
                "path": imageUrl,
                "isCover": true, // First image is cover
                "type": imageData['type'],
                "isCustom": true,
                "height": height,
              });

              coverPhoto.value =
                  imageList[0]["path"]; // Set first image as cover
              coverImageIndex.value = 0;
            }
            app.resolve<CustomDialogs>().hideCircularDialog(context);
          }
        },
        failureCallback: (message, statusCode) {
          app.resolve<CustomDialogs>().hideCircularDialog(context);
          log("Error: $message");
        },
      );
    } catch (e) {
      app.resolve<CustomDialogs>().hideCircularDialog(context);
      log("Exception: $e");
    }
  }

  callApiForCreatePost(
      {required BuildContext context,
      bool isPublished = false,
      required double height}) {
    FocusScope.of(context).unfocus();
    if (isPublished) isLoading.value = true;
    if (!isPublished) isDraftLoading.value = true;
    // bool isPrivate = visibilityName.value.toLowerCase() == "private";
    Map<String, dynamic> dict = {
      "title": titleController.value.text.trim(),
      "description": addNofollowToLinks(
          cleanHtmlStyles(cleanupHtmlBrTags(htmlContent.value))),
      "isPrivate": false,
      if (isPublished) "isPublished": isPublished
    };

    if (subProject.value?.id != null) {
      dict["ProjectId"] = subProject.value?.id;
    } else if (project.value?.id != null) {
      dict["ProjectId"] = project.value?.id;
    }

    if (imageList.isNotEmpty) {
      dict["media"] = imageList.asMap().entries.map((entry) {
        int index = entry.key;
        var image = entry.value;

        return {
          "link": image["path"],
          "type": image["type"] ?? "image/jpeg",
          if (imageList[index]["isCover"])
            "isCoverImage": imageList[index]["isCover"],
          if (imageList[index]["isCover"])
            "isCustom": imageList[index]["isCustom"],
          "height": image["height"],
        };
      }).toList();
    }
    if (height != 0) {
      dict["properties"] = jsonEncode({"height": height}).toString();
    }

    log('noti ===> ${mentionDropdownList.length}');

    if (mentionList.isNotEmpty && isPublished) {
      dict["Notifications"] =
          mentionList.toSet().toList().map((e) => e.toJson()).toList();
    }


    return apiManager.callApi(
      APIS.post.createPost,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            if (isPublished) isLoading.value = false;
            if (!isPublished) isDraftLoading.value = false;
            if (args != null && args["isCreate"] != null ||
                args != null && args["isCreateProject"] != null) {
              if (isCrossButton.value) {
                if(Get.isRegistered<ProjectDetailViewController>(tag: project.value?.id.toString())){
                  Get.find<ProjectDetailViewController>(
                      tag: project.value?.id.toString())
                      .currentSelectedIndex
                      .value = 5;
                  Get.find<ProjectDetailViewController>(
                      tag: project.value?.id.toString())
                      .scrollController
                      .animateTo(Get.find<ProjectDetailViewController>(
                      tag: project.value?.id.toString())
                      .scrollController.position.maxScrollExtent,
                      duration: Duration(milliseconds: 100),
                      curve: Curves.easeInOutSine);
                } else {
                  Get.find<SubProjectDetailViewController>()
                      .currentSelectedIndex
                      .value = 4;
                  Get.find<SubProjectDetailViewController>()
                      .scrollController
                      .animateTo(Get.find<SubProjectDetailViewController>()
                      .scrollController.position.maxScrollExtent,
                      duration: Duration(milliseconds: 100),
                      curve: Curves.easeInOutSine);
                }
                Get.close(1);
              } else {
                if (isPublished) {
                  if (!Get.isRegistered<ProjectDetailViewController>(
                      tag: project.value?.id.toString())) {
                    Get.put(ProjectDetailViewController(),
                            tag: project.value?.id.toString())
                        .currentSelectedIndex
                        .value = 0;
                  } else {
                    Get.find<ProjectDetailViewController>(
                            tag: project.value?.id.toString())
                        .currentSelectedIndex
                        .value = 0;
                  }
                  if (Get.isRegistered<SubProjectDetailViewController>()) {
                    Get.find<SubProjectDetailViewController>()
                        .currentSelectedIndex
                        .value = 0;
                  } else {
                    Get.put(SubProjectDetailViewController())
                        .currentSelectedIndex
                        .value = 0;
                  }
                  Get.find<ProjectDetailViewController>(
                          tag: project.value?.id.toString())
                      .scrollController
                      .animateTo(0,
                          duration: Duration(milliseconds: 100),
                          curve: Curves.easeInOutSine);
                  Get.find<SubProjectDetailViewController>()
                      .scrollController
                      .animateTo(0,
                          duration: Duration(milliseconds: 100),
                          curve: Curves.easeInOutSine);
                  if (args != null &&
                      args["isSubProject"] != null &&
                      args["isSubProject"] == true) {
                    Get.offNamedUntil(
                        Routes.post_detail,
                        (route) =>
                            route.settings.name == Routes.subProject_detail,
                        arguments: {
                          "postID": response['data']['id'],
                          "isDetailOpened": true,
                          "index": 0,
                          "source": "sub-project",
                          "projectId": project.value?.id,
                        })?.then(
                      (value) {
                        final subProjectController =
                            !Get.isRegistered<SubProjectDetailViewController>()
                                ? Get.put(SubProjectDetailViewController())
                                : Get.find<SubProjectDetailViewController>();
                        subProjectController.page.value = 1;
                        subProjectController.hasMoreData.value = true;
                        subProjectController.postDataList.clear();
                        subProjectController.callApiForUserGetPost(
                            context: Get.context!,
                            isPublished: subProjectController
                                    .currentSelectedIndex.value ==
                                4);
                      },
                    );
                  } else {
                    Get.offNamedUntil(Routes.post_detail,
                        (route) => route.settings.name == Routes.project_detail,
                        arguments: {
                          "postID": response['data']['id'],
                          "isDetailOpened": true,
                          "index": 0,
                          "source": "project",
                          "projectId": project.value?.id,
                        })?.then(
                      (value) {
                        final projectController =
                            !Get.isRegistered<ProjectDetailViewController>(
                                    tag: project.value?.id.toString())
                                ? Get.put(ProjectDetailViewController(),
                                    tag: project.value?.id.toString())
                                : Get.find<ProjectDetailViewController>(
                                    tag: project.value?.id.toString());
                        projectController.page.value = 1;
                        projectController.hasMoreData.value = true;
                        projectController.postDataList.clear();
                        projectController.callApiForUserGetPost(
                            context: Get.context!,
                            isPublished:
                                projectController.currentSelectedIndex.value ==
                                    5);
                      },
                    );
                  }
                } else {
                  if(Get.isRegistered<ProjectDetailViewController>(tag: project.value?.id.toString())){
                    Get.find<ProjectDetailViewController>(
                        tag: project.value?.id.toString())
                        .currentSelectedIndex
                        .value = 5;
                    Get.find<ProjectDetailViewController>(
                        tag: project.value?.id.toString())
                        .scrollController
                        .animateTo(Get.find<ProjectDetailViewController>(
                        tag: project.value?.id.toString())
                        .scrollController.position.maxScrollExtent,
                        duration: Duration(milliseconds: 100),
                        curve: Curves.easeInOutSine);
                  } else {
                    Get.find<SubProjectDetailViewController>()
                        .currentSelectedIndex
                        .value = 4;
                    Get.find<SubProjectDetailViewController>()
                        .scrollController
                        .animateTo(Get.find<SubProjectDetailViewController>()
                        .scrollController.position.maxScrollExtent,
                        duration: Duration(milliseconds: 100),
                        curve: Curves.easeInOutSine);
                  }
                  Get.close(2);
                }
              }
              if (!isPublished) {
                CommonFunction.showCustomSnackbar(
                  message: "Post saved to draft successfully.",
                  backgroundColor: AppTheme.warning,
                  isDraft: true,
                );
              } else {
                CommonFunction.showCustomSnackbar(
                  message: response['message'],
                );
              }
            } else {
              if (isPublished) {
                if(args != null && args["isProfile"] != null) {
                  Get.offNamedUntil(Routes.post_detail,
                          (route) => route.settings.name == Routes.profile,
                      arguments: {
                        "postID": response['data']['id'],
                        "isDetailOpened": true,
                        "index": 0,
                      })?.then((value) {
                    final controller = Get.find<ProfileViewController>();
                    controller.page.value = 1;
                    controller.hasMoreData.value = true;
                    controller.postDataList.clear();
                    controller.callApiForUserGetPost(
                        context: Get.context!);
                      },);
                } else {
                  Post newPost = Post.fromJson(response['data']);
                  newPost.user = CurrentUser.user;
                  await box.write("newPost", newPost);
                  Get
                      .find<ExploreController>()
                      .page
                      .value = 1;
                  Get
                      .find<ExploreController>()
                      .hasMoreData
                      .value = true;
                  await Get.find<ExploreController>()
                      .callApiForExplorePost(context: context);
                  Get.offNamedUntil(Routes.post_detail,
                          (route) => route.settings.name == Routes.Bottom_Bar,
                      arguments: {
                        "postID": response['data']['id'],
                        "isDetailOpened": true,
                        "index": 0,
                      });
                }
              } else {
                if(args != null && args["isProfile"] != null) {
                  final controller = Get.find<ProfileViewController>();
                  controller.currentSelectedIndex.value = 2;
                  controller.page.value = 1;
                  controller.hasMoreData.value = true;
                  controller.draftDataList.clear();
                  controller.callApiForUserGetPost(
                      context: Get.context!,isPublished: true);
                  if (isCrossButton.value) {
                    Get.close(1);
                  } else {
                    Get.close(2);
                  }
                } else {

                  Get.offNamedUntil(Routes.Bottom_Bar,
                          (route) => route.settings.name == Routes.Bottom_Bar);
                  Get
                      .find<BottomBarController>()
                      .currentIndex
                      .value = 0;
                }
              }
              if (!isPublished) {
                CommonFunction.showCustomSnackbar(
                  message: "Post saved to draft successfully.",
                  backgroundColor: AppTheme.warning,
                  isDraft: true,
                );
              }
            }
            imageList.value = [];
            titleController.clear();
            htmlContent.value = "";
          }
        } catch (error) {
          if (isPublished) isLoading.value = false;
          if (!isPublished) isDraftLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isButtonVisible.value = true;
        CommonFunction.showCustomSnackbar(
          message: statusCode,
          backgroundColor: AppTheme.red,
          isError: true,
        );
        if (isPublished) isLoading.value = false;
        if (!isPublished) isDraftLoading.value = false;
      },
    );
  }

  callApiForCreateCommunityPost(
      {required BuildContext context, required double height}) {
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "title": titleController.value.text.trim(),
      "description": addNofollowToLinks(
          cleanHtmlStyles(cleanupHtmlBrTags(htmlContent.value))),
      "isPrivate": false,
      "isPublished": true,
      "CommunityId": communityId.value,
    };
    if (height != 0) {
      dict["properties"] = jsonEncode({"height": height}).toString();
    }

    if (imageList.isNotEmpty) {
      dict["media"] = imageList.asMap().entries.map((entry) {
        int index = entry.key;
        var image = entry.value;

        return {
          "link": image["path"],
          "type": image["type"] ?? "image/jpeg",
          if (imageList[index]["isCover"])
            "isCoverImage": imageList[index]["isCover"],
          if (imageList[index]["isCover"])
            "isCustom": imageList[index]["isCustom"],
          "height": image["height"],
        };
      }).toList();
    }
    if (mentionList.isNotEmpty) {
      dict["Notifications"] =
          mentionList.toSet().toList().map((e) => e.toJson()).toList();
    }

    return apiManager.callApi(
      APIS.post.createPost,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // Get.close(2);
            if(args != null && args["communityUserId"] != null && args["communityUserId"] != CurrentUser.user.id) {
              Get.close(2);
            } else {
              Get.offNamedUntil(Routes.post_detail,
                      (route) => route.settings.name == Routes.community_detail,
                  arguments: {
                    "postID": response['data']['id'],
                    "isDetailOpened": true,
                    "index": 0,
                    "source": "community"
                  })?.then(
                    (value) {
                  final Communitycontroller =
                  !Get.isRegistered<CommunityDetailViewController>()
                      ? Get.put(CommunityDetailViewController())
                      : Get.find<CommunityDetailViewController>();
                  Communitycontroller.page.value = 1;
                  Communitycontroller.hasMoreData.value = true;
                  Communitycontroller.communityPostDataList.clear();
                  Communitycontroller.callApiForGetCommunityPost(
                    context: context,
                  );
                },
              );
            }
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            isLoading.value = false;
            imageList.value = [];
            titleController.clear();
            htmlContent.value = "";
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  String cleanupHtmlBrTags(String htmlContent) {
    String trimmed = htmlContent.trim();
    final RegExp endingBrTags =
        RegExp(r'(?:<br\s*\/?>[\s\n]*)+$', caseSensitive: false);
    String cleaned = trimmed.replaceAll(endingBrTags, '');
    return "$cleaned<br>";
  }

  Future<void> callApiForUpdatePost(
      {required BuildContext context,
      bool isPublished = false,
      bool shouldReload = false,
      required double height}) async {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
        ApiModel("/posts/${postId.value}", APIType.PATCH);
    if (isPublished) isLoading.value = true;
    if (!isPublished) isDraftLoading.value = true;
    // bool isPrivate = visibilityName.value.toLowerCase() == "private";
    Map<String, dynamic> dict = {
      "title": titleController.value.text.trim(),
      "description": addNofollowToLinks(
          cleanHtmlStyles(cleanupHtmlBrTags(htmlContent.value))),
      "isPrivate": false,
      "isPublished": isPublished
    };
    if (height != 0) {
      dict["properties"] = jsonEncode({"height": height}).toString();
    }
    if (subProject.value?.id != null) {
      dict["ProjectId"] = subProject.value?.id;
    } else if (project.value?.id != null) {
      dict["ProjectId"] = project.value?.id;
    }

    if (imageList.isNotEmpty) {
      dict["media"] = imageList.asMap().entries.map((entry) {
        int index = entry.key;
        var image = entry.value;

        return {
          "link": image["path"],
          "type": image["type"] ?? "image/jpeg",
          if (imageList[index]["isCover"])
            "isCoverImage": imageList[index]["isCover"],
          if (imageList[index]["isCover"])
            "isCustom": imageList[index]["isCustom"],
          "height": image["height"],
        };
      }).toList();
    } else {
      dict["media"] = [];
    }

    return apiManager.callApi(
      updatePost,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            if (isPublished) isLoading.value = false;
            if (!isPublished) isDraftLoading.value = false;
            if (isPublished) {
              if (args != null &&
                  args["isSubProjectEdit"] != null &&
                  args["isSubProjectEdit"] == true) {
                await _navigateToSubProject();
              } else if (args != null &&
                  args["isProjectEdit"] != null &&
                  args["isProjectEdit"] == true) {
                await _navigateToProject();
              } else if (args != null &&
                  args["isProfile"] != null &&
                  args["isProfile"] == true) {
                await _navigateToProfile();
              } else {
                Get.close(2);
              }
            } else {
              if (shouldReload) {
                Get.back();
              }
                Get.back();
            }
            imageList.value = [];
            titleController.clear();
            htmlContent.value = "";
          }
        } catch (error) {
          if (isPublished) isLoading.value = false;
          if (!isPublished) isDraftLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isButtonVisible.value = true;
        CommonFunction.showCustomSnackbar(
          message: "Title and description are required!",
          isError: true,
          backgroundColor: Color(0xFFEF3B41),
        );
        if (isPublished) isLoading.value = false;
        if (!isPublished) isDraftLoading.value = false;
      },
    );
  }

  Future<void> _navigateToSubProject() async {
    await Get.offNamedUntil(Routes.post_detail,
            (route) => route.settings.name == Routes.subProject_detail,
        arguments: {
          "postID": postId.value,
          "isDetailOpened": true,
          "index": 0,
          "source": "sub-project",
          "projectId": project.value?.id,
        })?.then(
          (value) {
        final subProjectController =
        !Get.isRegistered<SubProjectDetailViewController>()
            ? Get.put(SubProjectDetailViewController())
            : Get.find<SubProjectDetailViewController>();
        subProjectController.page.value = 1;
        subProjectController.hasMoreData.value = true;
        subProjectController.postDataList.clear();
        subProjectController.callApiForUserGetPost(
            context: Get.context!,
            isPublished:
            subProjectController.currentSelectedIndex.value ==
                4);
      },
    );
  }

  Future<void> _navigateToProject() async {
    await Get.offNamedUntil(Routes.post_detail,
            (route) => route.settings.name == Routes.project_detail,
        arguments: {
          "postID": postId.value,
          "isDetailOpened": true,
          "index": 0,
          "source": "project",
          "projectId": project.value?.id,
        })?.then(
          (value) {
        final projectController =
        !Get.isRegistered<ProjectDetailViewController>(
            tag: project.value?.id.toString())
            ? Get.put(ProjectDetailViewController(),
            tag: project.value?.id.toString())
            : Get.find<ProjectDetailViewController>(
            tag: project.value?.id.toString());
        projectController.page.value = 1;
        projectController.hasMoreData.value = true;
        projectController.postDataList.clear();
        projectController.callApiForUserGetPost(
            context: Get.context!,
            isPublished:
            projectController.currentSelectedIndex.value == 5);
      },
    );
  }

  Future<void> _navigateToProfile() async {
    await Get.offNamedUntil(Routes.post_detail,
            (route) => route.settings.name == Routes.profile,
        arguments: {
          "postID": postId.value,
          "isDetailOpened": true,
          "index": 0,
          "source": "profile",
        })?.then(
          (value) {
        final profileController =
        !Get.isRegistered<ProfileViewController>()
            ? Get.put(
          ProfileViewController(),
        )
            : Get.find<ProfileViewController>();
        profileController.page.value = 1;
        profileController.hasMoreData.value = true;
        args != null && args["isDraft"] == true
            ? profileController.draftDataList.clear()
            : profileController.postDataList.clear();
        profileController.callApiForUserGetPost(
            context: Get.context!,isPublished: args != null && args["isDraft"] == true ? true: false);
      },
    );
  }


  Future<void> callApiForUpdateCommunityPost(
      {required BuildContext context, required double height}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
        ApiModel("/posts/${postId.value}", APIType.PATCH);
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "title": titleController.value.text.trim(),
      "description": addNofollowToLinks(
          cleanHtmlStyles(cleanupHtmlBrTags(htmlContent.value))),
      "isPrivate": false,
      "isPublished": true
    };
    if (height != 0) {
      dict["properties"] = jsonEncode({"height": height}).toString();
    }

    if (imageList.isNotEmpty) {
      dict["media"] = imageList.asMap().entries.map((entry) {
        int index = entry.key;
        var image = entry.value;

        return {
          "link": image["path"],
          "type": image["type"] ?? "image/jpeg",
          if (imageList[index]["isCover"])
            "isCoverImage": imageList[index]["isCover"],
          if (imageList[index]["isCover"])
            "isCustom": imageList[index]["isCustom"],
          "height": image["height"],
        };
      }).toList();
    } else {
      dict["media"] = [];
    }

    return apiManager.callApi(
      updatePost,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isLoading.value = false;
            // Get.back();
            // Get.back();
            if(args != null && args['isCommunityDetail'] != null &&
                args['isCommunityDetail'] == true) {
              Get.close(2);
            } else {
              Get.offNamedUntil(Routes.post_detail,
                      (route) => route.settings.name == Routes.community_detail,
                  arguments: {
                    "postID": postId.value,
                    "isDetailOpened": true,
                    "index": 0,
                    "source": "community"
                  })?.then(
                    (value) {
                  final Communitycontroller =
                  !Get.isRegistered<CommunityDetailViewController>()
                      ? Get.put(CommunityDetailViewController())
                      : Get.find<CommunityDetailViewController>();
                  Communitycontroller.page.value = 1;
                  Communitycontroller.hasMoreData.value = true;
                  Communitycontroller.communityPostDataList.clear();
                  Communitycontroller.callApiForGetCommunityPost(
                    context: context,
                  );
                },
              );
            }
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            imageList.value = [];
            titleController.clear();
            htmlContent.value = "";
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isButtonVisible.value = true;
        CommonFunction.showCustomSnackbar(
          message: "Title and description are required!",
          isError: true,
          backgroundColor: Color(0xFFEF3B41),
        );
        isLoading.value = false;
      },
    );
  }

  callApiForGetOnePost({required BuildContext context}) {
    isGetPostLoading.value = true;
    final ApiModel getPost = ApiModel("/posts/${postId.value}", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            getPostDetailData.value = Post.fromJson(response["data"]);
            titleController.text = getPostDetailData.value.title;
            String htmlContent = getPostDetailData.value.description.trim();
            htmlContent = htmlContent.replaceFirst(RegExp(r'^(<p><br\s*/?></p>|<br\s*/?>)', caseSensitive: false), '');
            if (getPostDetailData.value.media.isNotEmpty) {
              for (var imageData in getPostDetailData.value.media) {
                String imageUrl = imageData.link;

                imageList.add({
                  "path": imageUrl,
                  "isCover": imageList.isEmpty, // First image is cover
                  "type": imageData.type,
                  "height": imageData.height,
                  "isCustom": imageData.isCustom,
                });

                if (imageList.length == 1) {
                  coverPhoto.value = imageUrl; // Set first image as cover
                }
              }
            }
            if (CurrentUser.user.id == getPostDetailData.value.user?.id) {
              project.value = getPostDetailData.value.project;
            }
            htmlContent = htmlContent.replaceAllMapped(
              RegExp(r'<iframe.*?src="(.*?)".*?</iframe>',
                  caseSensitive: false),
              (match) {
                final url = match.group(1) ?? '';
                return '<video src="$url"></video>';
              },
            );
            var delta = HtmlToDelta().convert(htmlContent);
            descriptionController.document = Document.fromDelta(delta);
            descriptionController.updateSelection(
              TextSelection.collapsed(offset: 0),
              ChangeSource.remote,
            );
            isGetPostLoading.value = false;
            update();
          }
        } catch (error) {
          isGetPostLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isGetPostLoading.value = false;
      },
    );
  }

  void _onTextChanged() {
    // if (!isSpellCheckEnabled.value || isChecking.value) return;

    _lastEditTime = DateTime.now();

    Future.delayed(_debounceInterval, () {
      final timeSinceLastEdit = DateTime.now().difference(_lastEditTime);
      if (timeSinceLastEdit >= _debounceInterval) {
        _checkSpelling();
      }
    });
  }

  Future<void> _checkSpelling() async {
    // if (isChecking.value) return;
    // isChecking.value = true;
    try {
      final text = descriptionController.document.toPlainText();
      // if (text.trim().isEmpty) {
      //   _clearSpellCheckFormat();
      //   isChecking.value = false;
      //   return;
      // }
      // final spellCheckResult = await spellCheckService.checkText(text);
      // _clearSpellCheckFormat();
      // suggestions.clear();
      // for (final error in spellCheckResult.errors) {
      //   _applySpellCheckFormat(error.offset, error.offset + error.length);
      //   suggestions[error.word] = error.suggestions;
      // }
      // _detectAndFormatUrls(text);
      _removeFormattingFromBrokenLinks(text);
    } catch (e) {
      debugPrint('Spell check error: $e');
    } finally {
      // isChecking.value = false;
    }
  }

  void _clearSpellCheckFormat() {
    final length = descriptionController.document.length;
    if (length <= 0) return;

    final delta = descriptionController.document.toDelta();

    int offset = 0;

    for (final op in delta.toList()) {
      if (op.key == 'insert' && op.value is String) {
        final attributes = op.attributes;
        final text = op.value as String;

        if (attributes != null &&
            attributes.containsKey('link') &&
            attributes['link'].toString().startsWith('spell-error://')) {
          final linkAttribute = Attribute.clone(Attribute.link, null);
          descriptionController.formatText(
            offset,
            text.length,
            linkAttribute,
            shouldNotifyListeners: true,
          );
        }
        offset += text.length;
      } else if (op.key == 'insert' && op.value is Map) {
        offset += 1;
      }
    }
  }


  void _detectAndFormatUrls(String text) {
    final urlRegex = RegExp(
      r'(?:(?:https?:\/\/)?(?:www\.)?)?[a-zA-Z0-9-]+(?:\.[a-zA-Z]{2,})(?:\/[^\s]*)?',
      caseSensitive: false,
    );

    final matches = urlRegex.allMatches(text);

    for (final match in matches) {
      final url = match.group(0)!;
      final start = match.start;
      final end = match.end;
      if (url.startsWith('http://') || url.startsWith('https://')) {
        continue;
      }
      final attribute = LinkAttribute("https://$url");
      descriptionController.formatText(
        start,
        end - start,
        attribute,
        shouldNotifyListeners: true,
      );
    }
  }

  void _removeFormattingFromBrokenLinks(String text) {
    final delta = descriptionController.document.toDelta();
    int currentIndex = 0;

    for (final op in delta.operations) {
      if(op.attributes != null &&
          op.attributes!.containsKey('link') &&
          (op.attributes!['link'].toString().startsWith('/user/') || op.attributes!['link'].toString().startsWith('/projects/'))) {
        continue; // Skip user and project links
      } else if (op.attributes != null && op.attributes!.containsKey('link')) {
        final linkText = op.data.toString();
        final linkStart = currentIndex;
        final linkEnd = currentIndex + linkText.length;

        if (linkText.contains(' ')) {
          descriptionController.formatText(
            linkStart,
            linkText.length,
            LinkAttribute(null),
            shouldNotifyListeners: true,
          );
        }

        if (linkStart < text.length && linkEnd <= text.length) {
          final actualText = text.substring(linkStart, linkEnd);
          if (actualText.contains(' ')) {
            descriptionController.formatText(
              linkStart,
              linkText.length,
              LinkAttribute(null),
              shouldNotifyListeners: true,
            );
          }
        }
      }
      currentIndex += op.length!;
    }
  }

  void _applySpellCheckFormat(int start, int end) {
    descriptionController.formatText(
      start,
      end - start,
      LinkAttribute('spell-error://$spellErrorKey'),
      shouldNotifyListeners: true,
    );
  }

  void showSuggestions(String misspelledWord, List<String> suggestions,
      Offset position, BuildContext context) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    showMenu(
      context: context,
      position: RelativeRect.fromRect(
        Rect.fromPoints(
          position,
          position.translate(1, 1),
        ),
        Offset.zero & overlay.size,
      ),
      items: [
        ...suggestions.map(
          (suggestion) => PopupMenuItem(
            value: suggestion,
            child: Text(suggestion),
          ),
        ),
        const PopupMenuItem(
          value: null,
          child: Divider(),
        ),
        const PopupMenuItem(
          value: 'ignore',
          child: Text('Ignore'),
        ),
      ],
    ).then((selectedValue) {
      if (selectedValue != null && selectedValue != 'ignore') {
        _replaceWord(misspelledWord, selectedValue);
      }
    });
  }

  void _replaceWord(String oldWord, String newWord) {
    final text = descriptionController.document.toPlainText();
    final regex = RegExp('\\b$oldWord\\b');
    final matches = regex.allMatches(text);

    final List<Match> matchList = matches.toList();
    for (int i = matchList.length - 1; i >= 0; i--) {
      final match = matchList[i];
      descriptionController.replaceText(
          match.start, match.end - match.start, newWord, null);
    }

    _checkSpelling();
  }

  void insertGenieContentToEditor(String content) {
    if (content.trim().isEmpty) return;
    final controller = descriptionController;
    final doc = controller.document;
    int docLength = doc.length;
    int insertOffset = 0;
    final selection = controller.selection;
    if (selection.isValid &&
        selection.baseOffset >= 0 &&
        selection.baseOffset <= docLength) {
      insertOffset = selection.baseOffset;
    } else {
      insertOffset = docLength;
    }
    insertOffset = insertOffset.clamp(0, docLength);

    final plainText = doc.toPlainText();
    if (docLength == 0) {
      controller.replaceText(
          0, 0, content, TextSelection.collapsed(offset: content.length));
      controller.updateSelection(
          TextSelection.collapsed(offset: content.length), ChangeSource.local);
    } else if (insertOffset == docLength) {
      if (!plainText.endsWith('\n')) {
        controller.replaceText(
            docLength, 0, '\n', TextSelection.collapsed(offset: docLength + 1));
        insertOffset++;
      }
      controller.replaceText(insertOffset, 0, content,
          TextSelection.collapsed(offset: insertOffset + content.length));
      controller.updateSelection(
          TextSelection.collapsed(offset: insertOffset + content.length),
          ChangeSource.local);
    } else {
      controller.moveCursorToEnd();
      controller.replaceText(insertOffset, 0, content,
          TextSelection.collapsed(offset: insertOffset + content.length));
      controller.updateSelection(
          TextSelection.collapsed(offset: insertOffset + content.length),
          ChangeSource.local);
    }
    descriptionController.formatText(
      selection.start,
      selection.end - selection.start,
      Attribute.bold,
    );
  }
}
