import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:incenti_ai/app/modules/other_user_profile/controllers/other_user_profile_controller.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_post_model.dart';
import '../../../../services/app_link_service.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/comment_bottomsheet_view.dart';
import '../../../../utillites/common_post_like_bottomsheet.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/typography.dart';
import '../../explore/components/common_post_widget.dart';
import '../../profile_view/views/read_more_view.dart';

Widget buildOtherProfileSection({
  required String fullName,
  required String email,
  required String about,
  required int followersCount,
  required int followingCount,
  required VoidCallback onFollowersTap,
  required VoidCallback onFollowingTap,
}) {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(40)),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Space.height(10),
        TypoGraphy(
          text: fullName,
          level: 6,
          fontWeight: FontWeight.w600,
        ),
        /*  TypoGraphy(
          text: email,
          level: 3,
          fontWeight: FontWeight.w400,
          color: AppTheme.grey,
        ),*/
        Space.height(15),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: onFollowersTap,
              child: TypoGraphy(
                text: "$followersCount followers",
                level: 4,
                fontWeight: FontWeight.w700,
              ),
            ),
            Space.width(7),
            TypoGraphy(
              text: "•",
              level: 4,
              fontWeight: FontWeight.w700,
            ),
            Space.width(7),
            GestureDetector(
              onTap: onFollowingTap,
              child: TypoGraphy(
                text: "$followingCount following",
                level: 4,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
        Space.height(15),
        seeMore(
          text: about,
          textAlign: TextAlign.start,
          maxLines: 2,
          fontSize: MySize.size16,
          fontWeight: FontWeight.w400,
        ),
        about.isNotEmpty ? Space.height(20) : const SizedBox(),
      ],
    ),
  );
}

Widget otherPostDataView(
    BuildContext context, OtherUserProfileController controller) {
  return CustomSliverListView(
    emptyWidget: Padding(
      padding: EdgeInsets.only(
        top: MySize.size25 ?? 100,
        left: paddingHoriZontal,
        right: paddingHoriZontal,
      ),
      child: Padding(
        padding: EdgeInsets.only(top: MySize.getScaledSizeHeight(70)),
        child: const Empty(
          title: "No Post available!",
        ),
      ),
    ),
    maximumReachedWidget: const SizedBox(),
    itemBuilder: (context, Post post, index) {
      Post res = controller.postDataList[index];
      return res.isPrivate == true
          ? const SizedBox()
          : Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: MySize.getScaledSizeWidth(10)),
              child: commonFeedView(
                res,
                index,
                context,
                onLike: () => controller.callApiForLikeProject(
                  context: context,
                  postId: res.id.toString(),
                  index: index,
                ),
                onLikeLongPress: () {
                  controller.postLikeList.clear();

                  controller.callApiForGetPostLike(
                      context: context, postId: res.id);
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(16),
                      ),
                    ),
                    builder: (context) => CommonPostLikeBottomSheet(
                      index: index,
                      isLoading: controller.isLoading,
                      postLikeList: controller.postLikeList,
                    ),
                  );
                },
                onRepostTap: () async {
                  Navigator.pop(context);
                  showCommonReportBottomSheet(
                    context: context,
                    title: "Report",
                    subTitle: "Why are you reporting this post?",
                    description:
                        "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                    options: controller.repostData,
                    onOptionTap: (selectedOption) async {
                      controller.selectedReason.value = selectedOption;
                      await controller.callApiForReportPost(
                        context: context,
                        postId: res.id.toString(),
                      );
                    },
                  );
                },
                onShare: () {
                  HapticFeedback.lightImpact();
                  controller.isShare.value = true;
                  AppLinkService().shareMedia(
                      slug: res.slug ?? '',
                      mediaType: ShareMediaType.posts,
                      title: res.title);
                },
                onComment: () {
                  controller.commentController.clear();
                  controller.callApiForGetCommentProject(
                      context: context,
                      postId: res.id.toString(),
                      index: index);
                  showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.vertical(top: Radius.circular(16))),
                      builder: (context) => CommonBottomSheet(
                            index: index,
                            isLoading: controller.isLoading,
                            commentController: controller.commentController,
                            commentDataList: controller.commentDataList,
                            commentFocusNode: controller.commentFocusNode,
                            userId: res.user?.id ?? 0,
                            onCommentSend: () {
                              controller.callApiForCommentProject(
                                  context: context,
                                  postId:
                                      "${controller.postDataList[index].id}",
                                  index: index);
                              controller.commentController.clear();
                            },
                          )).then((value) {
                    controller.callApiForGetCommentProject(
                        context: context,
                        postId: res.id.toString(),
                        index: index);
                          },);
                },
                onBookmark: () => controller.callApiForBookMarkProject(
                  context: context,
                  postId: res.id.toString(),
                  index: index,
                ),
                isLiked: res.isLiked?.value ?? false,
                likesCount: res.likesCount?.value ?? 0,
                isBookmarked: res.isBookMarked?.value ?? false,
                commentsCount: res.commentsCount?.value ?? 0, isShare: controller.isShare,
              ),
            );
    },
    isLoading: controller.apiManager.isLoading,
    items: controller.postDataList,
    hasMoreData: controller.hasMoreData.value,
    onLoadMore: () {
      return controller.callApiForUserGetPost(
        context: context,
        userId: controller.userList.value.id.toString(),
      );
    },
  );
}
