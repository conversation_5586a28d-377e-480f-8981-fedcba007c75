import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../services/app_link_service.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/typography.dart';

Column showCommunityBottomView(BuildContext context, {required RxBool isShare,bool isPost = false,required String communitySlug,required String title,void Function()? onTap}) {
  return Column(
    children: [
      Space.height(40),
      InkWell(
        onTap: (){
          HapticFeedback.lightImpact();
          isShare.value = true;
          AppLinkService().shareMedia(slug: communitySlug, mediaType: ShareMediaType.communities,title: title);
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(AppImage.share,
                height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithBase,),
            Space.width(20),
            TypoGraphy(
              text: "Share",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(41),
      InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(AppImage.report,
                height: MySize.size24, width: MySize.size24,color: AppTheme.whiteWithBase,),
            Space.width(20),
            TypoGraphy(
              text: isPost ? "Report this Post" : "Report this Community",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(61),
    ],
  );
}

Column showCommunityEditBottomView(
    {required BuildContext context, required void Function() onEdit, required void Function() onDelete}) {
  return Column(
    children: [
      Space.height(40),
      InkWell(
        onTap: onEdit,
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.editIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.whiteWithBase,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Edit",
              level: 5,
            )
          ],
        ),
      ),
      Space.height(41),
      InkWell(
        onTap: onDelete,
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.trashIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.red,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Delete",
              level: 5,
              color: AppTheme.red,
            )
          ],
        ),
      ),
      Space.height(61),
    ],
  );
}
