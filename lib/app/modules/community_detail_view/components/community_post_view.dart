import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/community_detail_view/controllers/community_detail_view_controller.dart';
import 'package:intl/intl.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_post_model.dart';
import '../../../../services/app_link_service.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/comment_bottomsheet_view.dart';
import '../../../../utillites/common_post_like_bottomsheet.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/delete_confirmation_dialog.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../explore/components/common_post_widget.dart';
import '../../profile_view/components/profile_widget_view.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import 'community_bottom_view.dart';

Widget communityPostDataView(
    BuildContext context, CommunityDetailViewController controller) {
  return CustomSliverListView(
    emptyWidget: Padding(
      padding: EdgeInsets.only(top: MySize.getScaledSizeHeight(150)),
      child: const Empty(
        title: "No Community Post available!",
      ),
    ),
    maximumReachedWidget: const SizedBox(),
    itemBuilder: (context, Post post, index) {
      Post res = controller.communityPostDataList[index];
      return res.isPrivate == true
          ? const SizedBox()
          : commonFeedView(res, index, context,
              onShare: () {
                HapticFeedback.lightImpact();
                controller.isShare.value = true;
                AppLinkService().shareMedia(
                    slug: res.slug ?? '',
                    mediaType: ShareMediaType.posts,
                    title: res.title);
              },
              onLikeLongPress: () {
                controller.postLikeList.clear();

                controller.callApiForGetPostLike(
                    context: context, postId: res.id);
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                  ),
                  builder: (context) => CommonPostLikeBottomSheet(
                    index: index,
                    isLoading: controller.isLoading,
                    postLikeList: controller.postLikeList,
                  ),
                );
              },
              headView: Row(
                children: [
                  InkWell(
                    onTap: () {
                      if (res.user?.id == CurrentUser.user.id) {
                        Get.toNamed(Routes.profile);
                      } else {
                        Get.toNamed(Routes.other_user_profile,
                            arguments: {"UserId": res.user?.id});
                      }
                    },
                    child: Container(
                      height: MySize.getScaledSizeHeight(42),
                      width: MySize.getScaledSizeWidth(42),
                      margin: EdgeInsets.symmetric(
                        vertical: MySize.getScaledSizeHeight(3),
                        horizontal: MySize.getScaledSizeWidth(3),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(50),
                        child: profileImage(
                            userName: res.user?.firstName ?? "",
                            url: res.user?.image ?? "",
                            width: MySize.getScaledSizeWidth(42),
                            height: MySize.getScaledSizeHeight(42),
                            borderColor: Colors.transparent,
                            color: AppTheme.darkGrey[100]),
                      ),
                    ),
                  ),
                  SizedBox(width: MySize.getScaledSizeWidth(12)),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        if (res.user?.id == CurrentUser.user.id) {
                          Get.toNamed(Routes.profile);
                        } else {
                          Get.toNamed(Routes.other_user_profile,
                              arguments: {"UserId": res.user?.id});
                        }
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    TypoGraphy(
                                      text:
                                          "${res.user?.firstName} ${res.user?.lastName}",
                                      level: 4,
                                      // color: AppTheme
                                      //     .baseBlack,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          TypoGraphy(
                            text: DateFormat('MMM d, y').format(
                                DateTime.parse(res.createdAt.toString())),
                            level: 2,
                            fontWeight: FontWeight.w400,
                            color: AppTheme.grey,
                          )
                        ],
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      ImagePickerBottomSheet.show(
                        context: context,
                        child: CurrentUser.user.id ==
                                controller.communityPostDataList[index].user
                                    ?.id /*|| Get.find<CommunitiesController>().selectedTabIndex.value == 0*/
                            ? showCommunityEditBottomView(
                                context: context,
                                onDelete: () {
                                  Navigator.pop(Get.context!);
                                  HapticFeedback.heavyImpact();
                                  showDeleteConfirmationDialog(
                                    context: Get.context!,
                                    description:
                                        "Are you sure you want to delete community post permanently?",
                                    onConfirm: () async {
                                      await controller
                                          .callApiForDeleteCommunityPost(
                                              context: context,
                                              communityId: res.id.toString())
                                          .then(
                                        (value) {
                                          controller.page.value = 1;
                                          controller.hasMoreData.value = true;
                                          controller.communityPostDataList
                                              .clear();
                                          return controller
                                              .callApiForGetCommunityPost(
                                            context: context,
                                          );
                                        },
                                      );
                                    },
                                    title: "Delete Community post",
                                    onCancel: () {
                                      Get.back();
                                    }, isLoading: controller.isLoading,
                                  );
                                },
                                onEdit: () {
                                  Navigator.pop(Get.context!);
                                  Get.toNamed(Routes.post, arguments: {
                                    "PostId": res.id,
                                    "isCommunity": true,
                                    "isCommunityEdit": true,
                                  })?.then(
                                    (value) {
                                      controller.page.value = 1;
                                      controller.hasMoreData.value = true;
                                      controller.communityPostDataList.clear();
                                      return controller
                                          .callApiForGetCommunityPost(
                                        context: context,
                                      );
                                    },
                                  );
                                },
                              )
                            : showCommunityBottomView(
                                context,
                                title: controller.getCommunityData.value.name ??
                                    '',
                                communitySlug:
                                    controller.getCommunityData.value.slug ??
                                        '',
                                isPost: true,
                                onTap: () {
                                  Navigator.pop(context);
                                  showCommonReportBottomSheet(
                                    context: context,
                                    title: "Report",
                                    subTitle:
                                        "Why are you reporting this post?",
                                    description:
                                        "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                                    options: controller.repostData,
                                    onOptionTap: (selectedOption) async {
                                      controller.selectedReason.value =
                                          selectedOption;
                                      await controller.callApiForReportPost(
                                        context: context,
                                        postId: res.id.toString(),
                                      );
                                    },
                                  );
                                }, isShare: controller.isShare,
                              ),
                      );
                    },
                    child: SvgPicture.asset(
                      AppImage.moreVertIcon,
                      height: MySize.getScaledSizeWidth(24),
                      width: MySize.getScaledSizeHeight(24),
                      color: Color(0xFF787E89),
                    ),
                  ),
                ],
              ),
              onTap: () {
                Get.toNamed(Routes.post_detail, arguments: {
                  "postID": res.id,
                  "index": index,
                  "source": "community"
                })?.then(
                  (value) {
                    controller.page.value = 1;
                    controller.hasMoreData.value = true;
                    controller.communityPostDataList.clear();
                    return controller.callApiForGetCommunityPost(
                      context: context,
                    );
                  },
                );
              },
              onLike: () => controller.callApiForLikeProject(
                    context: context,
                    postId: res.id.toString(),
                    index: index,
                  ),
              onComment: () {
                controller.callApiForGetCommentProject(
                    context: context, postId: res.id.toString(), index: index);
                showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(16))),
                    builder: (context) => CommonBottomSheet(
                          index: index,
                          isLoading: controller.isLoading,
                          commentController: controller.commentController,
                          commentDataList: controller.commentDataList,
                          commentFocusNode: controller.commentFocusNode,
                          onCommentSend: () {
                            controller.callApiForCommentProject(
                                context: context,
                                postId:
                                    "${controller.communityPostDataList[index].id}",
                                index: index);
                            controller.commentController.clear();
                          },
                          userId: res.user?.id ?? 0,
                        )).then(
                  (value) {
                    controller.callApiForGetCommentProject(
                        context: context,
                        postId: res.id.toString(),
                        index: index);
                  },
                );
              },
              onBookmark: () => controller.callApiForBookMarkProject(
                    context: context,
                    postId: res.id.toString(),
                    index: index,
                  ),
              isLiked: res.isLiked?.value ?? false,
              likesCount: res.likesCount?.value ?? 0,
              isBookmarked: res.isBookMarked?.value ?? false,
              commentsCount: res.commentsCount?.value ?? 0,
              isUser: true, isShare: controller.isShare);
    },
    isLoading: controller.isCommunityPostLoading.value,
    items: controller.communityPostDataList,
    hasMoreData: controller.hasMoreData.value,
    onLoadMore: () {
      return controller.callApiForGetCommunityPost(
        context: context,
      );
    },
  );
}

Widget buildTopBarCommunity(BuildContext context,
    {required CommunityDetailViewController controller}) {
  return Column(
    children: [
      Space.height(55),
      Padding(
        padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
        child: Row(
          children: [
            InkWell(
              child:
                  buildIcon(AppImage.backArrow, () => Get.back(), padding: 7),
            ),
            Spacer(),
            buildMoreOptionsIcon(context, controller: controller),
          ],
        ),
      ),
    ],
  );
}

Widget buildMoreOptionsIcon(BuildContext context,
    {required CommunityDetailViewController controller}) {
  return buildIcon(AppImage.moreVertIcon, () {
    HapticFeedback.lightImpact();
    ImagePickerBottomSheet.show(
      context: context,
      child: CurrentUser.user.id == controller.getCommunityData.value.UserId
          ? showCommunityEditBottomView(
              context: context,
              onDelete: () {
                Navigator.pop(Get.context!);
                HapticFeedback.heavyImpact();
                showDeleteConfirmationDialog(
                  context: Get.context!,
                  description:
                      "Are you sure you want to delete community permanently?",
                  onConfirm: () async {
                    await controller.callApiForDeleteOneCommunity(
                        context: context,
                        communityId:
                            controller.getCommunityData.value.id.toString());
                  },
                  title: "Delete Community",
                  onCancel: () {
                    Get.back();
                  }, isLoading: controller.isLoading,
                );
              },
              onEdit: () {
                Navigator.pop(Get.context!);
                Get.toNamed(Routes.create_community, arguments: {
                  "communityId": controller.getCommunityData.value.id
                })?.then(
                  (value) {
                    controller.callApiForGetOneCommunity(
                        context: Get.context!,
                        communityId: controller.communityId.value.toString());
                  },
                );
              },
            )
          : showCommunityBottomView(
              context,
              title: controller.getCommunityData.value.name ?? '',
              communitySlug: controller.getCommunityData.value.slug ?? '',
              onTap: () {
                Navigator.pop(context);
                showCommonReportBottomSheet(
                  context: context,
                  title: "Report",
                  subTitle: "Why are you reporting this community?",
                  description:
                      "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                  options: controller.repostData,
                  onOptionTap: (selectedOption) async {
                    controller.selectedReason.value = selectedOption;
                    await controller.callApiForReportCommunity(
                      context: context,
                      communityId: controller.communityId.value.toString(),
                    );
                  },
                );
              }, isShare: controller.isShare,
            ),
    );
  }, padding: 9);
}
