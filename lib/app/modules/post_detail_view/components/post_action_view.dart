import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/project_detail_view/controllers/project_detail_view_controller.dart';
import 'package:incenti_ai/app/modules/sub_project_detail_view/controllers/sub_project_detail_view_controller.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../services/app_link_service.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/comment_bottomsheet_view.dart';
import '../../../../utillites/common_post_like_bottomsheet.dart';
import '../../../../utillites/typography.dart';
import '../../community_detail_view/controllers/community_detail_view_controller.dart';
import '../../explore/controllers/explore_controller.dart';
import '../../global_search/controllers/global_search_view_controller.dart';
import '../../profile_view/controllers/profile_view_controller.dart';

class CommonPostActions extends StatelessWidget {
  final dynamic controller;
  final String source;
  final int postId;
  final int index;
  final int? projectId;
  final RxBool isShare;
  final dynamic postDetailData;
  final VoidCallback? onLikePressed;
  final VoidCallback? onCommentPressed;
  final VoidCallback? onBookmarkPressed;
  final VoidCallback? onSharePressed;

  const CommonPostActions({
    super.key,
    required this.controller,
    required this.source,
    required this.postId,
    required this.index,
    required this.isShare,
    required this.postDetailData,
    this.onLikePressed,
    this.projectId,
    this.onCommentPressed,
    this.onBookmarkPressed,
    this.onSharePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
          bottom: MySize.getScaledSizeHeight(15),
          top: MySize.getScaledSizeHeight(15)),
      child: Obx(
        () => Row(
          spacing: MySize.size20 ?? 20,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLikeButton(context),
            _buildCommentButton(context),
            _buildBookmarkButton(context),
            _buildShareButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildLikeButton(BuildContext context) {
    return InkWell(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onLongPress: () => _handleLikeLongPress(context),
      onTap: () => _handleLikeTap(context),
      child: _buildActionButton(
        icon: postDetailData.value.isLiked?.value == true
            ? AppImage.fillHeart
            : AppImage.heartIcon,
        isActive: postDetailData.value.isLiked?.value == true,
        count: postDetailData.value.likesCount?.value,
      ),
    );
  }

  Widget _buildCommentButton(BuildContext context) {
    return InkWell(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: () => _handleCommentTap(context),
      child: _buildActionButton(
        icon: AppImage.commentIcon,
        isActive: false,
        count: postDetailData.value.commentsCount?.value,
        iconHeight: 22,
        iconWidth: 22,
      ),
    );
  }

  Widget _buildBookmarkButton(BuildContext context) {
    return InkWell(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: () => _handleBookmarkTap(context),
      child: Container(
        height: MySize.getScaledSizeHeight(60),
        width: MySize.getScaledSizeWidth(60),
        decoration: BoxDecoration(
          color:
              box.read('isDarkMode') ? AppTheme.baseBlack : AppTheme.subPrimary,
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: const EdgeInsets.all(18),
          child: SvgPicture.asset(
            postDetailData.value.isBookMarked?.value == true
                ? AppImage.fillBookMark
                : AppImage.bookmarkIcon,
            height: MySize.getScaledSizeHeight(18),
            width: MySize.getScaledSizeWidth(18),
            color: postDetailData.value.isBookMarked?.value == true
                ? null
                : Color(0xFF787e89),
          ),
        ),
      ),
    );
  }

  Widget _buildShareButton(BuildContext context) {
    return InkWell(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: () => _handleShareTap(context, isShare),
      child: Container(
        height: MySize.getScaledSizeHeight(60),
        width: MySize.getScaledSizeWidth(60),
        decoration: BoxDecoration(
          color:
              box.read('isDarkMode') ? AppTheme.baseBlack : AppTheme.subPrimary,
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: const EdgeInsets.all(18),
          child: SvgPicture.asset(
            AppImage.share,
            height: MySize.getScaledSizeHeight(18),
            width: MySize.getScaledSizeWidth(18),
            color: Color(0xFF787e89),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String icon,
    required bool isActive,
    int? count,
    double iconHeight = 18,
    double iconWidth = 18,
  }) {
    return Container(
      height: MySize.getScaledSizeHeight(60),
      width: MySize.getScaledSizeWidth(60),
      decoration: BoxDecoration(
        color:
            box.read('isDarkMode') ? AppTheme.baseBlack : AppTheme.subPrimary,
        shape: BoxShape.circle,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            icon,
            height: MySize.getScaledSizeHeight(iconHeight),
            width: MySize.getScaledSizeHeight(iconWidth),
            color: isActive ? null : Color(0xFF787e89),
          ),
          if (count != null && count != 0) ...[
            Space.width(5),
            TypoGraphy(
              text: "$count",
              textStyle: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w400,
                fontFamily: "Inter",
                color:
                    box.read('isDarkMode') ? AppTheme.white : AppTheme.darkGrey,
              ),
            )
          ]
        ],
      ),
    );
  }

  void _handleLikeLongPress(BuildContext context) {
    if (onLikePressed != null) {
      onLikePressed!();
      return;
    }

    controller.postLikeList.clear();
    controller.callApiForGetPostLike(context: context, postId: postId);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => CommonPostLikeBottomSheet(
        index: index,
        isLoading: controller.isLoading,
        postLikeList: controller.postLikeList,
      ),
    );
  }

  void _handleLikeTap(BuildContext context) {
    HapticFeedback.lightImpact();

    if (source == "Notification") {
      controller.callApiForLikeProject(
          context: context, postId: postId.toString());
    } else {
      Get.find<ExploreController>().callApiForLikeProject(
        context: context,
        index: index,
        postId: postId.toString(),
      );
    }

    // Update like status
    postDetailData.update((val) {
      if (val != null) {
        val.isLiked?.value = !(val.isLiked?.value ?? true);
        if (val.isLiked?.value ?? true) {
          val.likesCount?.value = (val.likesCount?.value ?? 0) + 1;
        } else {
          val.likesCount?.value = (val.likesCount?.value ?? 0) - 1;
        }
      }
    });
  }

  void _handleCommentTap(BuildContext context) {
    if (onCommentPressed != null) {
      onCommentPressed!();
      return;
    }

    switch (source) {
      case "community":
        _handleCommunityComment(context);
        break;
      case "profile":
        _handleProfileComment(context);
        break;
      case "globalSearch":
        _handleGlobalSearchComment(context);
        break;
      case "Notification":
        _handleNotificationComment(context);
        break;
      case "project":
        _handleProjectComment(context);
        break;
      case "sub-project":
        _handleSubProjectComment(context);
        break;
      default:
        _handleExploreComment(context);
        break;
    }
  }

  void _handleCommunityComment(BuildContext context) {
    final communityController = Get.find<CommunityDetailViewController>();
    communityController.commentController.clear();
    communityController.callApiForGetCommentProject(
      context: context,
      postId: postId.toString(),
      index: index,
    );

    _showCommentBottomSheet(
      context: context,
      controller: communityController,
      userId: postDetailData.value.user?.id ?? 0,
      onCommentSend: () {
        communityController.callApiForCommentProject(
          context: context,
          postId: postId.toString(),
          index: index,
        );
        communityController.commentController.clear();
      },
      onClose: () {
        postDetailData.update((val) {
          if (val != null) {
            val.commentsCount?.value =
                communityController.commentDataList.length;
          }
        });
      },
    );
  }

  void _handleProfileComment(BuildContext context) {
    final profileController = Get.find<ProfileViewController>();
    profileController.commentController.clear();
    profileController.callApiForGetCommentProject(
      context: context,
      postId: postId.toString(),
      index: index,
    );

    _showCommentBottomSheet(
      context: context,
      controller: profileController,
      userId: postDetailData.value.user?.id ?? 0,
      onCommentSend: () {
        profileController.callApiForCommentProject(
          context: context,
          postId: postId.toString(),
          index: index,
        );
        profileController.commentController.clear();
      },
      onClose: () {
        postDetailData.update((val) {
          if (val != null) {
            val.commentsCount?.value = profileController.commentDataList.length;
          }
        });
      },
    );
  }

  void _handleGlobalSearchComment(BuildContext context) {
    final globalSearchController = Get.find<GlobalSearchViewController>();
    globalSearchController.commentController.clear();
    globalSearchController.callApiForGetCommentProject(
      context: context,
      postId: postId.toString(),
      index: index,
    );

    _showCommentBottomSheet(
      context: context,
      controller: globalSearchController,
      userId: postDetailData.value.user?.id ?? 0,
      onCommentSend: () {
        globalSearchController.callApiForCommentProject(
          context: context,
          postId: postId.toString(),
          index: index,
        );
        globalSearchController.commentController.clear();
      },
      onClose: () {
        postDetailData.update((val) {
          if (val != null) {
            val.commentsCount?.value =
                globalSearchController.commentDataList.length;
          }
        });
      },
    );
  }

  void _handleNotificationComment(BuildContext context) {
    controller.commentController.clear();
    controller.callApiForGetCommentProject(
        context: context, postId: postId.toString());

    _showCommentBottomSheet(
      context: context,
      controller: controller,
      userId: postDetailData.value.user?.id ?? 0,
      onCommentSend: () {
        controller.callApiForCommentProject(
            context: context, postId: postId.toString());
        controller.commentController.clear();
      },
      onClose: () {
        postDetailData.update((val) {
          if (val != null) {
            val.commentsCount?.value = controller.commentDataList.length;
          }
        });
      },
    );
  }

  void _handleExploreComment(BuildContext context) {
    final exploreController = Get.find<ExploreController>();
    exploreController.commentController.clear();
    exploreController.callApiForGetCommentProject(
      context: context,
      postId: postId.toString(),
      index: index,
    );

    _showCommentBottomSheet(
      context: context,
      controller: exploreController,
      userId: postDetailData.value.user?.id ?? 0,
      onCommentSend: () {
        exploreController.callApiForCommentProject(
          context: context,
          postId: postId.toString(),
          index: index,
        );
        exploreController.commentController.clear();
      },
      onClose: () {
        postDetailData.update((val) {
          if (val != null) {
            val.commentsCount?.value = exploreController.commentDataList.length;
          }
        });
      },
    );
  }

  void _handleProjectComment(BuildContext context) {
    final projectController =
        Get.put(ProjectDetailViewController(), tag: projectId.toString());
    projectController.commentController.clear();
    projectController.callApiForGetCommentProject(
      context: context,
      postId: postId.toString(),
      index: index,
    );
    _showCommentBottomSheet(
      context: context,
      controller: projectController,
      userId: postDetailData.value.user?.id ?? 0,
      onCommentSend: () {
        projectController.callApiForCommentProject(
          context: context,
          postId: postId.toString(),
          index: index,
        );
        projectController.commentController.clear();
      },
      onClose: () {
        postDetailData.update((val) {
          if (val != null) {
            val.commentsCount?.value = projectController.commentDataList.length;
          }
        });
      },
    );
  }

  void _handleSubProjectComment(BuildContext context) {
    final projectController =
    Get.put(SubProjectDetailViewController());
    projectController.commentController.clear();
    projectController.callApiForGetCommentProject(
      context: context,
      postId: postId.toString(),
      index: index,
    );
    _showCommentBottomSheet(
      context: context,
      controller: projectController,
      userId: postDetailData.value.user?.id ?? 0,
      onCommentSend: () {
        projectController.callApiForCommentProject(
          context: context,
          postId: postId.toString(),
          index: index,
        );
        projectController.commentController.clear();
      },
      onClose: () {
        postDetailData.update((val) {
          if (val != null) {
            val.commentsCount?.value = projectController.commentDataList.length;
          }
        });
      },
    );
  }

  void _showCommentBottomSheet({
    required BuildContext context,
    required dynamic controller,
    required int userId,
    required VoidCallback onCommentSend,
    required VoidCallback onClose,
  }) {
    showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return CommonBottomSheet(
              index: index,
              isLoading: controller.isLoading,
              commentController: controller.commentController,
              commentDataList: controller.commentDataList,
              commentFocusNode: controller.commentFocusNode,
              onCommentSend: onCommentSend,
              userId: userId,
            );
          },
        );
      },
    ).then((value) => onClose());
  }

  void _handleBookmarkTap(BuildContext context) {
    if (onBookmarkPressed != null) {
      onBookmarkPressed!();
      return;
    }
    if (source == "Notification") {
      controller.callApiForBookMarkProject(
          context: context, postId: postId.toString());
    } else {
      Get.find<ExploreController>().callApiForBookMarkProject(
        context: context,
        index: index,
        postId: postId.toString(),
      );
    }

    postDetailData.update((val) {
      if (val != null) {
        val.isBookMarked?.value = !(val.isBookMarked?.value ?? true);
      }
    });
  }

  void _handleShareTap(BuildContext context,RxBool isShare) {
    if (onSharePressed != null) {
      onSharePressed!();
      return;
    }

    HapticFeedback.lightImpact();
    isShare.value = true;
    AppLinkService().shareMedia(
      slug: postDetailData.value.slug ?? '',
      mediaType: ShareMediaType.posts,
      title: postDetailData.value.title,
    );
  }
}
