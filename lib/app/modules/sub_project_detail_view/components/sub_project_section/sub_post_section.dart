import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/sub_project_detail_view/controllers/sub_project_detail_view_controller.dart';

import '../../../../../constants/app_image.dart';
import '../../../../../constants/app_size_constant.dart';
import '../../../../../models/app_post_model.dart';
import '../../../../../models/app_project_model.dart';
import '../../../../../models/section_base.dart';
import '../../../../../services/app_link_service.dart';
import '../../../../../utillites/app_theme.dart';
import '../../../../../utillites/comment_bottomsheet_view.dart';
import '../../../../../utillites/common_post_like_bottomsheet.dart';
import '../../../../../utillites/common_shimmer_effect.dart';
import '../../../../../utillites/current_user.dart';
import '../../../../../utillites/custom_sliver_list_view.dart';
import '../../../../../utillites/empty.dart';
import '../../../../routes/app_pages.dart';
import '../../../explore/components/common_post_widget.dart';
import '../../../explore/components/common_widget_view.dart';
import '../../../user_detail/components/image_picker_bottom_sheet.dart';

class PostsSection extends SectionBase<SubProjectDetailViewController> {
  PostsSection({required super.controller});

  @override
  String get title => 'Posts';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: AppTheme.primary1,
      // shape: const CircleBorder(),
      onPressed: () async {
        HapticFeedback.lightImpact();

        Get.toNamed(Routes.post, arguments: {
          "isCreateProject": true,
          "isCrossVisible": false,
          "isSubProject": true,
          "projectModel": Project(
              id: controller.getProjectDetailData.value.id,
              parentProjectData: ParentProjectData(
                id: controller
                        .getProjectDetailData.value.parentProjectData?.id ??
                    0,
                name: controller
                        .getProjectDetailData.value.parentProjectData?.name ??
                    "",
                image: controller
                        .getProjectDetailData.value.parentProjectData?.image ??
                    "",
                subProjectsCount: controller.getProjectDetailData.value
                        .parentProjectData?.subProjectsCount ??
                    "",
              ),
              name: controller
                      .getProjectDetailData.value.parentProjectData?.name ??
                  "",
              image: controller
                      .getProjectDetailData.value.parentProjectData?.image ??
                  "",
              subProjectsCount:
                  controller.getProjectDetailData.value.subProjectsCount)
        })?.then(
          (value) {
            controller.page.value = 1;
            controller.hasMoreData.value = true;
            controller.postDataList.clear();
            controller.callApiForUserGetPost(
                context: Get.context!,
                isPublished: controller.currentSelectedIndex.value == 4);
          },
        );
      },
      child: SvgPicture.asset(
        controller.iconMap[controller.currentSelectedIndex.value] ??
            AppImage.postIcon,
        height: MySize.size70,
      ),
    );
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      Obx(() {
        return controller.apiManager.isLoading &&
                controller.postDataList.isEmpty
            ? SliverToBoxAdapter(child: ShimmerPostCard())
            : CustomSliverListView(
                emptyWidget: Padding(
                  padding: EdgeInsets.only(
                    top: MySize.size25 ?? 100,
                    left: paddingHoriZontal,
                    right: paddingHoriZontal,
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(top: MySize.size50 ?? 50),
                    child: const Empty(
                      title: "No Post available!",
                    ),
                  ),
                ),
                maximumReachedWidget: const SizedBox(),
                itemBuilder: (context, Post post, index) {
                  Post res = controller.postDataList[index];
                  return res.isPrivate == true
                      ? const SizedBox()
                      : commonFeedView(
                          onTap: () {
                            Get.toNamed(Routes.post_detail, arguments: {
                              "postID": res.id,
                              "index": index,
                              "source": "sub-project",
                              "projectId": res.projectId,
                            })?.then(
                              (value) async {
                                controller.page.value = 1;
                                controller.hasMoreData.value = true;
                                controller.postDataList.clear();
                                await controller.callApiForUserGetPost(
                                  context: Get.context!,
                                );
                              },
                            );
                          },
                          onLikeLongPress: () {
                            controller.postLikeList.clear();

                            controller.callApiForGetPostLike(
                                context: context, postId: res.id);
                            showModalBottomSheet(
                              context: context,
                              isScrollControlled: true,
                              backgroundColor: Colors.transparent,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(16),
                                ),
                              ),
                              builder: (context) => CommonPostLikeBottomSheet(
                                index: index,
                                isLoading: controller.isLoading,
                                postLikeList: controller.postLikeList,
                              ),
                            );
                          },
                          onShare: () {
                            HapticFeedback.lightImpact();
                            controller.isShare.value = true;
                            controller.shareContentType.value = "post"; // Set content type for post sharing
                            AppLinkService().shareMedia(
                                slug: res.slug ?? '',
                                mediaType: ShareMediaType.posts,
                                title: res.title);
                          },
                          onFeedHeadTap:
                              controller.getProjectDetailData.value.userId ==
                                      CurrentUser.user.id
                                  ? () {
                                      HapticFeedback.lightImpact();
                                      ImagePickerBottomSheet.show(
                                          context: context,
                                          child: showUserPostOption(
                                              postId: res.id,
                                              isPin: res.pinnedAt,
                                              title: res.title,
                                              isSubProjectEdit: true, isShare: controller.isShare,));
                                    }
                                  : null,
                          res,
                          index,
                          context,
                          onLike: () => controller.callApiForLikeProject(
                                context: context,
                                postId: res.id.toString(),
                                index: index,
                              ),
                          onComment: () {
                            controller.commentController.clear();
                            controller.callApiForGetCommentProject(
                                context: context,
                                postId: res.id.toString(),
                                index: index);
                            showModalBottomSheet(
                                context: context,
                                isScrollControlled: true,
                                backgroundColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(16))),
                                builder: (context) => CommonBottomSheet(
                                      index: index,
                                      userId: res.user?.id ?? 0,
                                      isLoading: controller.isLoading,
                                      commentController:
                                          controller.commentController,
                                      commentDataList:
                                          controller.commentDataList,
                                      commentFocusNode:
                                          controller.commentFocusNode,
                                      onCommentSend: () {
                                        controller.callApiForCommentProject(
                                            context: context,
                                            postId:
                                                "${controller.postDataList[index].id}",
                                            index: index);
                                        controller.commentController.clear();
                                      },
                                    )).then(
                              (value) {
                                controller.callApiForGetCommentProject(
                                    context: context,
                                    postId: res.id.toString(),
                                    index: index);
                              },
                            );
                          },
                          onBookmark: () =>
                              controller.callApiForBookMarkProject(
                                context: context,
                                postId: res.id.toString(),
                                index: index,
                              ),
                          isPostEditUser: (controller.getProjectDetailData.value
                                  .projectMembers.isNotEmpty &&
                              controller.getProjectDetailData.value
                                      .projectMembers[0].access ==
                                  "write"),
                          isLiked: res.isLiked?.value ?? false,
                          likesCount: res.likesCount?.value ?? 0,
                          isBookmarked: res.isBookMarked?.value ?? false,
                          commentsCount: res.commentsCount?.value ?? 0,
                          isUser: res.user?.id == CurrentUser.user.id
                              ? true
                              : false, isShare: controller.isShare,);
                },
                isLoading: controller.apiManager.isLoading,
                items: controller.postDataList,
                hasMoreData: controller.hasMoreData.value,
                onLoadMore: () {
                  return controller.callApiForUserGetPost(
                    context: context,
                  );
                },
              );
      })
    ];
  }

  @override
  void onCategorySelected() {
    controller.page.value = 1;
    controller.hasMoreData.value = true;
    controller.postDataList.clear();
    controller.callApiForUserGetPost(context: Get.context!);
  }
}
